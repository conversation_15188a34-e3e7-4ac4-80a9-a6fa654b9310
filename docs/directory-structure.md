# ContentForge Directory Structure

This document outlines the directory structure for ContentForge, a product built with a Next.js frontend, a Node.js/Express.js backend, Supabase for database and authentication, and n8n for workflow orchestration. The structure is designed to ensure clarity, modularity, and ease of maintenance.

## Root Directory

- **`.env.example`**  
  Sample environment variable file for development setup (e.g., Supabase URL, API keys).  
- **`.gitignore`**  
  Specifies files and directories to exclude from version control (e.g., `node_modules`, `.env`).  
- **`.eslintrc.json`**  
  ESLint configuration for code linting across the project.  
- **`.prettierrc`**  
  Prettier configuration for consistent code formatting.  
- **`README.md`**  
  Project overview, setup instructions, and basic documentation.  
- **`package.json`**  
  Defines dependencies and scripts for the entire project (frontend and backend).  
- **`docker-compose.yml`**  
  Configuration for local development with Dockerized services (e.g., backend, database).

## Source Code (`src/`)

### Frontend (`src/frontend/`)

The frontend is built with Next.js and TypeScript, deployed on Vercel.

- **`pages/`**  
  Next.js pages for routing and rendering.  
  - `index.tsx`: Homepage  
  - `login.tsx`: Login page  
  - `etc.`: Additional pages as needed  
- **`components/`**  
  Reusable React components, organized by feature.  
  - `auth/`  
    - `LoginForm.tsx`: Login form component  
  - `content/`  
    - `ContentInput.tsx`: Content input component  
  - `etc.`: Other feature-specific components  
- **`styles/`**  
  CSS files for styling.  
  - `global.css`: Global styles  
  - `components/`  
    - `Button.module.css`: Component-specific CSS modules  
- **`public/`**  
  Static assets served by Next.js.  
  - `images/`: Image files  
  - `fonts/`: Font files  
- **`types/`**  
  TypeScript type definitions for the frontend.  
  - `user.ts`: User-related types  
  - `etc.`: Additional types  
- **`__tests__/`**  
  Tests for frontend code.  
  - `unit/`  
    - `components/LoginForm.test.tsx`: Unit tests for components  
  - `integration/`  
    - `pages/login.test.tsx`: Integration tests for pages  
- **`tsconfig.json`**  
  TypeScript configuration specific to the frontend.

### Backend (`src/backend/`)

The backend is built with Node.js, Express.js, and TypeScript, containerized with Docker.

- **`controllers/`**  
  Logic for handling API requests.  
  - `authController.ts`: Authentication endpoints  
  - `contentController.ts`: Content-related endpoints  
- **`models/`**  
  Database models (e.g., for Supabase).  
  - `User.ts`: User model  
  - `Content.ts`: Content model  
- **`routes/`**  
  Express.js route definitions.  
  - `authRoutes.ts`: Authentication routes  
  - `contentRoutes.ts`: Content routes  
- **`middleware/`**  
  Custom Express.js middleware.  
  - `authMiddleware.ts`: Authentication middleware  
- **`config/`**  
  Configuration files for the backend.  
  - `databaseConfig.ts`: Database connection settings  
- **`types/`**  
  TypeScript type definitions for the backend.  
  - `api.ts`: API response types  
- **`__tests__/`**  
  Tests for backend code.  
  - `unit/`  
    - `controllers/authController.test.ts`: Unit tests for controllers  
  - `integration/`  
    - `routes/authRoutes.test.ts`: Integration tests for routes  
- **`tsconfig.json`**  
  TypeScript configuration specific to the backend.  
- **`Dockerfile`**  
  Docker configuration for building and running the backend.

## Database (`database/`)

Contains Supabase-related files.

- **`migrations/`**  
  SQL migration scripts for database schema changes.  
  - `001_create_users_table.sql`: Initial user table creation  

## Workflows (`workflows/`)

Stores n8n workflow files for orchestration.

- **`text_to_blog_workflow.json`**  
  Example workflow for converting text to blog content.  

## Tests (`tests/`)

End-to-end tests for the entire application.

- **`e2e/`**  
  - `content_flow.spec.ts`: Tests for content creation flow  

## Documentation (`docs/`)

Project documentation for developers and users.

- **`api/`**  
  - `auth.md`: API documentation for authentication endpoints  
- **`architecture/`**  
  - `overview.md`: High-level architecture overview  
- **`user_guides/`**  
  - `getting_started.md`: User guide for initial setup  

## Notes

- **Environment Variables**: Sensitive data (e.g., Supabase keys) is stored in `.env` files, with `.env.example` providing a template.  
- **Testing**: Unit and integration tests are colocated with their respective frontend and backend code, while end-to-end tests are in the root `tests/` folder.  
- **Deployment**: The backend is containerized with Docker, while the frontend is deployed on Vercel.  

This structure supports modular development, clear separation of concerns, and scalability for future features.