# Backend Structure Document for ContentForge

## 1. Introduction

### 1.1 Purpose
This document provides a detailed and comprehensive description of the backend structure for ContentForge, a digital content automation platform that streamlines the creation, management, and distribution of content across multiple digital platforms, including YouTube, Instagram, LinkedIn, and blogs. The backend is designed to support the platform’s core functionalities, such as content transformation, workflow orchestration, and multi-platform publishing, while adhering to strict resource constraints and security requirements. This document serves as a guide for developers, architects, and stakeholders to understand the backend’s architecture, components, and operational mechanisms.

### 1.2 Scope
The backend structure encompasses the microservices architecture, API layer, database design, integration mechanisms, security framework, and deployment strategy for ContentForge’s Minimum Viable Product (MVP). It addresses the technical requirements outlined in the product description and project kickoff documents, including modularity, scalability, and operation within a zero-budget environment using free-tier services and open-source tools. The document also covers performance optimizations for limited hardware (8GB RAM MacBook Pro) and future scalability considerations.

### 1.3 Definitions, Acronyms, and Abbreviations
| Term | Definition |
|------|------------|
| **ContentForge** | The digital content automation platform. |
| **MVP** | Minimum Viable Product, the initial version with core features. |
| **API** | Application Programming Interface. |
| **RBAC** | Role-Based Access Control. |
| **RLS** | Row-Level Security. |
| **PWA** | Progressive Web App. |
| **n8n** | Open-source workflow automation tool used for orchestration. |
| **Supabase** | Open-source Firebase alternative providing PostgreSQL and authentication. |

### 1.4 References
- Product Description ([Product Description](attachment id:0 type:text_file filename:product_description.md))
- Project Kickoff ([Project Kickoff](attachment id:1 type:text_file filename:project_kickoff.md))

## 2. Architecture Overview
ContentForge’s backend is built on a microservices architecture, where each major functionality is encapsulated in a separate, containerized service using Docker. This approach ensures modularity, scalability, and fault isolation. Services communicate via RESTful APIs or message queues, enabling efficient data flow and independent scaling. The architecture is optimized for resource-constrained environments, leveraging free-tier cloud services and open-source tools to meet the zero-budget requirement.

### 2.1 Microservices
The backend comprises the following microservices, each responsible for a specific domain of functionality:

| **Microservice** | **Purpose** | **Key Responsibilities** |
|-------------------|-------------|--------------------------|
| **User Management Service** | Manages user authentication and profiles | User registration, login, RBAC, profile updates |
| **Content Management Service** | Handles content storage and metadata | Content CRUD operations, versioning, search, collaboration |
| **Workflow Service** | Orchestrates content processing workflows | Workflow creation, execution, monitoring via n8n |
| **Transformation Service** | Transforms raw content into platform-ready formats | Text generation, image processing, video creation, audio synthesis |
| **Publishing Service** | Distributes content to external platforms | Platform API integration, OAuth, scheduling, publishing |
| **Analytics Service** | Collects and stores performance metrics | Data retrieval from platforms, storage, reporting |
| **Notification Service** | Sends user notifications | Email and in-app alerts for content status, errors |

Each microservice is containerized using Docker, allowing independent deployment and scaling. Services are designed to be stateless where possible, with state managed in the database.

### 2.2 API Layer
The API layer, implemented using Next.js API routes, serves as the primary interface between the frontend and backend services. It exposes endpoints for all major functionalities, ensuring secure and efficient communication.

| **Endpoint** | **Purpose** | **Example Request** | **Example Response** |
|--------------|-------------|---------------------|----------------------|
| `/api/users` | User management | `POST /api/users { email, password }` | `{ user_id, token }` |
| `/api/content` | Content CRUD | `POST /api/content { title, input_type, data }` | `{ content_id, status }` |
| `/api/workflows` | Workflow management | `POST /api/workflows { name, steps }` | `{ workflow_id, status }` |
| `/api/publish` | Trigger publishing | `POST /api/publish { content_id, platforms }` | `{ publication_id, status }` |
| `/api/analytics` | Retrieve analytics | `GET /api/analytics?content_id=123` | `{ metrics: { views, likes } }` |
| `/api/notifications` | Manage notifications | `POST /api/notifications { user_id, message }` | `{ notification_id, status }` |

### 2.3 Database Layer
Supabase PostgreSQL is the primary database, providing real-time data synchronization and row-level security (RLS). The database schema is optimized for content relationships and efficient querying.

| **Entity** | **Attributes** | **Description** |
|------------|----------------|-----------------|
| **Users** | id, email, password (hashed), name, role, preferences | Stores user information and roles |
| **Content** | id, user_id, title, input_type, input_data, status, created_at, updated_at | Stores raw and processed content |
| **Workflows** | id, name, description, steps (JSON), created_by, created_at | Defines transformation and publishing workflows |
| **PublishedContent** | id, content_id, platform, publication_date, status, url | Tracks published content |
| **Analytics** | id, published_content_id, platform, metrics (JSON), date | Stores performance metrics |

**Relationships**:
- Users have many Contents (1:N).
- Content is associated with one Workflow (1:1).
- Content can have multiple PublishedContents (1:N).
- PublishedContent has multiple Analytics records (1:N).

RLS policies ensure that users can only access their own data or data shared with their team.

### 2.4 Integration Layer
The Publishing Service integrates with external platforms (e.g., YouTube, Instagram, LinkedIn) using their APIs. OAuth flows are implemented to obtain and refresh access tokens, which are stored securely in the database. The Analytics Service periodically fetches performance data from these platforms, adhering to API rate limits.

### 2.5 Security Layer
Security is a core component of the backend, with the following measures:

- **Authentication**: Supabase Auth handles user login, supporting email/password and social logins (e.g., Google, X).
- **Authorization**: RBAC restricts access based on roles (e.g., admin, editor, viewer).
- **Encryption**: TLS for data in transit, Supabase encryption for data at rest.
- **Input Validation**: Sanitizes inputs to prevent SQL injection, XSS, and other attacks using libraries like DOMPurify ([DOMPurify](https://github.com/cure53/DOMPurify)).
- **API Security**: JWT tokens for endpoint authentication, rate limiting to prevent abuse.
- **Secrets Management**: API keys and tokens stored in environment variables.

### 2.6 Monitoring and Logging
- **Error Tracking**: Sentry ([Sentry](https://sentry.io)) monitors errors and sends alerts.
- **Logging**: ELK stack (Elasticsearch, Logstash, Kibana) for centralized logging.
- **Performance Monitoring**: Vercel Analytics ([Vercel](https://vercel.com)) tracks API response times and system performance.

## 3. Backend Processing Modules
The Transformation Service includes specialized modules for content processing, each running in a separate Docker container:

| **Module** | **Functionality** | **Technologies** |
|------------|-------------------|------------------|
| **Text Generation** | Generates blog posts, captions, scripts | NLP libraries (e.g., Hugging Face Transformers) |
| **Image Processing** | Resizes, crops, enhances images | ImageMagick, Sharp |
| **Video Creation** | Creates videos from text, images, audio | FFmpeg, OpenCV |
| **Audio Processing** | Synthesizes voice-overs, audio snippets | Text-to-speech APIs (e.g., Google TTS) |

These modules are optimized for resource efficiency, using progressive processing and caching to minimize memory usage on the constrained 8GB RAM hardware.

## 4. Workflow Orchestration
The Workflow Service uses n8n ([n8n](https://n8n.io)), an open-source workflow automation tool, to manage content transformation and publishing workflows. Users can create custom workflows via a visual interface, defining steps like:

- Input processing (e.g., extract text from a PDF).
- Transformation (e.g., generate a video from text).
- Publishing (e.g., post to Instagram).

n8n supports parallel processing and real-time status updates, integrating with other services via APIs.

## 5. Content Management
The Content Management Service handles the content lifecycle:

- **Storage**: Stores raw and transformed content in Supabase, supporting files up to 50MB.
- **Versioning**: Tracks content changes with timestamps and user IDs.
- **Search and Filter**: Enables querying by metadata (e.g., title, status) using PostgreSQL full-text search.
- **Collaboration**: Supports team access with role-based permissions.

## 6. Publishing and Distribution
The Publishing Service manages content distribution:

- **Authentication**: Uses OAuth to connect to platform APIs, storing tokens securely.
- **Optimization**: Applies platform-specific formatting (e.g., Instagram image dimensions).
- **Scheduling**: Queues content for future publication using a content calendar.
- **Error Handling**: Retries failed publications and logs errors for user notification.

## 7. Analytics and Reporting
The Analytics Service fetches performance metrics from platform Ascertainable platforms, storing data in the database. Metrics include views, likes, shares, and clicks. The service provides APIs for the frontend to display dashboards and reports.

## 8. Notification System
The Notification Service uses Resend ([Resend](https://resend.com)) for email notifications and in-app alerts for events like:

- Content publication status.
- Workflow completion or errors.
- Scheduled publication reminders.

## 9. Performance Requirements
- **Workflow Processing**: Simple workflows (e.g., text to social media post) complete in under 5 minutes; complex workflows (e.g., video generation) in under 30 minutes.
- **Concurrent Workflows**: Supports at least 5 concurrent workflows.
- **API Response Time**: Under 2 seconds for most requests.
- **Concurrent Users**: Handles 100 concurrent users.

## 10. Deployment and Scalability
- **Deployment**: Docker containers with Docker Compose for development, Kubernetes for production if scaled.
- **Scalability**: Horizontal scaling of microservices, load balancing for API traffic.
- **Caching**: Redis for frequently accessed data to reduce database load.
- **Cloud Services**: Free-tier Supabase and Vercel for hosting and database.

## 11. Development and Testing
- **Tools**: Git/GitHub for version control, Postman/Insomnia for API testing, Docker for containerization.
- **Testing**:
  - Unit tests with Jest.
  - Integration tests for service interactions.
  - End-to-end tests with Cypress.
- **CI/CD**: GitHub Actions for automated testing and deployment.

## 12. Constraints and Optimizations
- **Hardware**: Optimized for 8GB RAM MacBook Pro with Intel Iris Graphics 6100 GPU.
- **Budget**: Zero budget, using free-tier services (Supabase, Vercel) and open-source tools (n8n, Docker).
- **Optimizations**:
  - Progressive processing for large files.
  - Memory-efficient algorithms and data structures.
  - Caching with Redis to minimize database queries.

## 13. Future Enhancements
- **AI Integration**: Advanced NLP and generative AI for content creation.
- **Multi-Tenancy**: Support for agencies managing multiple clients.
- **Platform Expansion**: Additional publishing platforms and content types.
- **Analytics**: Predictive insights and A/B testing.

## 14. Conclusion
ContentForge’s backend structure is a robust, modular system that supports automated content creation, management, and distribution. Its microservices architecture, built with Docker, Supabase, and n8n, ensures scalability, security, and efficiency, even under strict resource constraints. The design provides a solid foundation for the MVP and future growth, meeting the needs of digital marketers, content creators, small businesses, agencies, and influencers.

## 15. References
- Product Description ([Product Description](attachment id:0 type:text_file filename:product_description.md))
- Project Kickoff ([Project Kickoff](attachment id:1 type:text_file filename:project_kickoff.md))