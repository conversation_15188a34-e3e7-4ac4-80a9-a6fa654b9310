# Developer Setup Guide

This guide will help you set up your development environment for ContentForge.

## Prerequisites

Before you begin, ensure you have the following installed:

- **Node.js**: Version 18.x or higher
- **npm**: Usually comes with Node.js
- **Git**: For version control
- **Docker** and **Docker Compose**: For containerized development
- **Supabase CLI**: For local database development (optional)

## Initial Setup

### 1. Clone the Repository

```bash
git clone https://github.com/your-username/contentforge.git
cd contentforge
```

### 2. Install Dependencies

Install dependencies for the entire project:

```bash
npm install
```

This will install dependencies for both the frontend and backend thanks to the workspace configuration.

### 3. Environment Variables

Create environment files for both frontend and backend:

**Frontend (.env.local in frontend directory)**:
```
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

**Backend (.env in backend directory)**:
```
PORT=4000
NODE_ENV=development
```

### 4. Start Development Servers

#### Option 1: Using npm scripts

Start the frontend development server:
```bash
npm run dev
```

In a separate terminal, start the backend:
```bash
cd backend
npm run dev
```

#### Option 2: Using Docker Compose

Start all services with Docker Compose:
```bash
docker-compose up
```

This will start the frontend, backend, and n8n services in development mode.

## Development Workflow

### Code Style and Linting

We use ESLint and Prettier for code quality and formatting. Run linting with:

```bash
npm run lint
```

### Testing

Run tests with:

```bash
npm test
```

### Building for Production

Build the application with:

```bash
npm run build
```

## Troubleshooting

### Common Issues

1. **Port conflicts**: If you see "port already in use" errors, check if you have other services running on ports 3000, 4000, or 5678.

2. **Node.js version issues**: Make sure you're using Node.js 18.x or higher. You can use nvm to manage multiple Node.js versions.

3. **Docker issues**: If Docker containers fail to start, try:
   ```bash
   docker-compose down -v
   docker-compose up --build
   ```

4. **Module not found errors**: Try deleting node_modules and reinstalling:
   ```bash
   rm -rf node_modules
   npm install
   ```

## Getting Help

If you encounter any issues not covered in this guide, please:

1. Check the existing GitHub issues
2. Ask for help in the project's communication channels
3. Create a new issue with detailed information about your problem
