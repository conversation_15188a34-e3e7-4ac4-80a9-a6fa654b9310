# Contributing to ContentForge

Thank you for your interest in contributing to ContentForge! This document provides guidelines and instructions for contributing to the project.

## Code of Conduct

By participating in this project, you agree to abide by our Code of Conduct. Please read it before contributing.

## Getting Started

### Issues

- Check existing issues to see if your problem or idea has already been addressed.
- For bugs, create a new issue with a clear description, steps to reproduce, expected behavior, and actual behavior.
- For feature requests, describe the feature, its benefits, and potential implementation approaches.

### Pull Requests

1. Fork the repository.
2. Create a new branch from `main`:
   ```bash
   git checkout -b feature/your-feature-name
   ```
3. Make your changes.
4. Run tests and linting:
   ```bash
   npm run lint
   npm test
   ```
5. Commit your changes using [Conventional Commits](https://www.conventionalcommits.org/):
   ```bash
   git commit -m "feat: add new feature"
   ```
6. Push to your fork:
   ```bash
   git push origin feature/your-feature-name
   ```
7. Create a pull request to the `main` branch of the original repository.

## Development Guidelines

### Coding Standards

- Follow the ESLint configuration provided in the project.
- Write clean, readable, and well-documented code.
- Use TypeScript for type safety.
- Follow the project's file and folder structure.

### Commit Messages

We use [Conventional Commits](https://www.conventionalcommits.org/) for commit messages:

- `feat`: A new feature
- `fix`: A bug fix
- `docs`: Documentation changes
- `style`: Changes that don't affect code functionality (formatting, etc.)
- `refactor`: Code changes that neither fix bugs nor add features
- `test`: Adding or modifying tests
- `chore`: Changes to build process, dependencies, etc.

Example: `feat: add user authentication system`

### Testing

- Write tests for new features and bug fixes.
- Ensure all tests pass before submitting a pull request.
- Aim for good test coverage.

### Documentation

- Update documentation when adding or changing features.
- Use clear, concise language.
- Include code examples where appropriate.

## Review Process

1. At least one maintainer must review and approve your pull request.
2. CI checks must pass.
3. Address any feedback from reviewers.
4. Once approved, a maintainer will merge your pull request.

## Development Setup

See the [Setup Guide](./setup-guide.md) for instructions on setting up your development environment.

## License

By contributing to ContentForge, you agree that your contributions will be licensed under the project's license.
