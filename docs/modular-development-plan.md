# Modular Development Plan for ContentForge

## 1. Introduction

### 1.1 Purpose
This document outlines a modular development plan for ContentForge, a digital content automation platform designed to streamline content creation, management, and distribution across platforms like YouTube, Instagram, and LinkedIn. By breaking the platform into independent development modules, we enable parallel development by different teams, ensuring that each module can be integrated without disrupting core functionality. The plan includes module definitions, team assignments, and strategies for integration, tailored to the platform’s microservices architecture and resource constraints (e.g., 8GB RAM MacBook Pro, zero budget).

### 1.2 Scope
The plan covers the identification of development modules, their responsibilities, dependencies, and integration points. It assigns modules to hypothetical teams, interpreting "Lovable" and "bolt" as potential team names or development focuses (e.g., frontend vs. backend). The document aligns with the product’s requirements from the product description and project kickoff, emphasizing modularity, scalability, and the use of free-tier tools like Docker, Supabase, Next.js, Vercel, and n8n.

### 1.3 Background
ContentForge automates the content lifecycle, offering features like a universal input portal, workflow orchestration, content transformation, multi-platform publishing, and centralized content management. Its microservices architecture supports modularity, allowing independent scaling and deployment. The development must occur within a 12-week timeline, leveraging open-source tools and free-tier services to meet budget constraints.

## 2. Development Modules

ContentForge is divided into eight development modules, each encapsulating a specific functionality. These modules are designed to minimize dependencies, enabling parallel development while ensuring seamless integration through well-defined APIs.

### 2.1 Module 1: Authentication and User Management
- **Responsibilities**:
  - User registration, login, and logout.
  - Profile management (e.g., name, email, preferences).
  - Role-based access control (RBAC) for roles like admin, editor, and viewer.
  - Integration with Supabase Auth for email/password and social logins (e.g., Google, X).
- **Technologies**: Supabase Auth, PostgreSQL, Next.js API routes.
- **Dependencies**: None (foundational module).
- **Integration Points**: Provides APIs for authentication (`/api/auth`) and user data (`/api/users`) used by all modules.
- **Example API**:
  - `POST /api/auth/login`: `{ email, password }` → `{ token, user_id }`
- **Development Team**: Backend Team (bolt).

### 2.2 Module 2: Content Management
- **Responsibilities**:
  - Store, retrieve, update, and delete content (raw and transformed).
  - Support versioning and content history.
  - Enable search and filtering by metadata (e.g., title, status).
  - Manage content relationships (e.g., user ownership, workflow associations).
- **Technologies**: Supabase PostgreSQL, full-text search extensions.
- **Dependencies**: Module 1 (for user authentication and ownership).
- **Integration Points**: Provides APIs for content CRUD operations (`/api/content`) to Frontend, Workflow Orchestration, and Publishing.
- **Example API**:
  - `POST /api/content`: `{ title, input_type, data }` → `{ content_id, status }`
- **Development Team**: Backend Team (bolt).

### 2.3 Module 3: Workflow Orchestration
- **Responsibilities**:
  - Integrate with n8n for creating, editing, and executing workflows.
  - Support workflow templates (e.g., “Social Media Blitz”) and customizations.
  - Monitor workflow status and handle errors in real-time.
- **Technologies**: n8n, Docker, RESTful APIs.
- **Dependencies**: Module 2 (for accessing content to process).
- **Integration Points**: Triggers transformations via Content Transformation and publishing via Publishing.
- **Example API**:
  - `POST /api/workflows`: `{ name, steps }` → `{ workflow_id, status }`
- **Development Team**: Backend Team (bolt), with n8n expertise.

### 2.4 Module 4: Content Transformation
- **Responsibilities**:
  - Transform raw content into platform-specific formats:
    - Text: Blog posts, social media posts, captions.
    - Images: Thumbnails, resized graphics.
    - Videos: Short-form clips, animations.
    - Audio: Voice-overs, podcast snippets.
  - Optimize transformations for resource-constrained environments.
- **Technologies**: NLP libraries (e.g., Hugging Face Transformers), ImageMagick, FFmpeg, text-to-speech APIs.
- **Dependencies**: Module 2 (for input content), Module 3 (for workflow instructions).
- **Integration Points**: Receives transformation requests from Workflow Orchestration and sends outputs to Publishing.
- **Example API**:
  - `POST /api/transform`: `{ content_id, type }` → `{ transformed_content_id }`
- **Development Team**: Backend Team (bolt), with media processing expertise.

### 2.5 Module 5: Publishing
- **Responsibilities**:
  - Integrate with external platform APIs (e.g., YouTube, Instagram, LinkedIn).
  - Handle OAuth for platform authentication.
  - Schedule and execute content publishing.
  - Manage publication status and errors.
- **Technologies**: Platform APIs, OAuth libraries, Docker.
- **Dependencies**: Module 4 (for transformed content).
- **Integration Points**: Publishes content to platforms and provides publication data to Analytics.
- **Example API**:
  - `POST /api/publish`: `{ content_id, platforms }` → `{ publication_id, status }`
- **Development Team**: Backend Team (bolt), or Integration Team if available.

### 2.6 Module 6: Analytics
- **Responsibilities**:
  - Fetch performance metrics (e.g., views, likes, shares) from published content.
  - Store analytics data in the database.
  - Provide APIs for frontend dashboards.
- **Technologies**: Platform APIs, Supabase PostgreSQL.
- **Dependencies**: Module 5 (for published content).
- **Integration Points**: Sends analytics data to Frontend for display.
- **Example API**:
  - `GET /api/analytics?content_id=123` → `{ metrics: { views, likes } }`
- **Development Team**: Backend Team (bolt), or Integration Team if available.

### 2.7 Module 7: Notifications
- **Responsibilities**:
  - Send email and in-app notifications for events (e.g., content published, workflow errors).
  - Integrate with email services like Resend.
- **Technologies**: Resend, Supabase for in-app notifications.
- **Dependencies**: None (requires hooks from other modules).
- **Integration Points**: Listens for events from Workflow Orchestration, Publishing, ව

- **Responsibilities**:
  - Send email and in-app notifications for events (e.g., content published, workflow errors).
  - Integrate with email services like Resend.
- **Technologies**: Resend, Supabase for in-app notifications.
- **Dependencies**: None (requires hooks from other modules).
- **Integration Points**: Listens for events from Workflow Orchestration, Publishing, etc.
- **Example API**:
  - `POST /api/notifications`: `{ user_id, message }` → `{ notification_id, status }`
- **Development Team**: Backend Team (bolt).

### 2.8 Module 8: Frontend
- **Responsibilities**:
  - Build the user interface using Next.js, including dashboards, content creation forms, workflow editors, and analytics displays.
  - Ensure responsiveness, accessibility, and PWA capabilities.
  - Integrate with backend APIs for all functionalities.
- **Technologies**: Next.js, React, Tailwind CSS or shadcn/UI, Vercel.
- **Dependencies**: All backend modules for APIs.
- **Integration Points**: Calls APIs from all other modules to display data and handle user interactions.
- **Development Team**: Frontend Team (Lovable).

## 3. Team Assignments

Assuming "Lovable" and "bolt" refer to team names or development focuses, we assign modules based on typical roles and expertise:

| **Team** | **Modules** | **Rationale** |
|----------|-------------|---------------|
| **Lovable (Frontend Team)** | Module 8 (Frontend) | Likely focuses on user-friendly, intuitive interfaces, aligning with frontend development for UI/UX. |
| **bolt (Backend Team)** | Modules 1–7 | Likely emphasizes performance and reliability, suitable for backend logic, integrations, and processing. |

If the team is small (e.g., 2–5 developers), the Backend Team can be subdivided:
- **Developer A**: Modules 1 (Authentication and User Management) and 2 (Content Management).
- **Developer B**: Modules 3 (Workflow Orchestration) and 4 (Content Transformation).
- **Developer C**: Modules 5 (Publishing), 6 (Analytics), and 7 (Notifications).

Alternatively, if "Lovable" and "bolt" represent development approaches:
- **Lovable**: Module 8 and user-facing parts of Module 2 (e.g., content search UI).
- **bolt**: Modules 1, 3–7 for performance-critical backend tasks.

## 4. Development Strategy

### 4.1 Parallel Development
To enable parallel development:
- **Clear APIs**: Each module exposes RESTful APIs with Swagger documentation (e.g., `/api/auth`, `/api/content`).
- **Mocking Dependencies**: Use tools like Postman or MSW to simulate APIs during development (e.g., mock `/api/transform` for Frontend).
- **Version Control**: Use Git with feature branches (e.g., `feature/auth-module`) and pull requests for integration.
- **CI/CD**: Implement GitHub Actions for automated testing and deployment to Vercel and Supabase.

### 4.2 Development Order
While parallel, some modules have dependencies:
1. **Modules 1 and 2**: Foundational for user and content data.
2. **Module 8**: Can start with UI for authentication and content management, using mocks.
3. **Modules 3 and 4**: Build on content management for workflows and transformations.
4. **Module 5**: Requires transformed content for publishing.
5. **Module 6**: Needs published content for analytics.
6. **Module 7**: Can be developed anytime with event hooks.

### 4.3 Integration
- **Regular Integration Testing**: Weekly tests using Cypress for end-to-end flows and Postman for API validation.
- **Staging Environment**: Deploy modules to a staging environment on Vercel for integration testing.
- **Error Handling**: Implement robust error handling in APIs to manage integration issues.
- **Documentation**: Maintain API specs and module guides in a shared repository (e.g., Confluence or GitHub Wiki).

## 5. Development Phases
Aligning with the project kickoff’s 12-week timeline:
- **Phase 1: Foundation (2 weeks)**:
  - Module 1: Basic authentication setup.
  - Module 2: Content storage schema.
  - Module 8: Basic frontend structure.
- **Phase 2: Core Functionality (4 weeks)**:
  - Module 2: Full content management.
  - Module 3: Workflow integration with n8n.
  - Module 4: Basic transformations (text, images).
  - Module 8: Content creation UI.
- **Phase 3: Integration & Testing (3 weeks)**:
  - Module 5: Platform integrations.
  - Module 6: Analytics APIs.
  - Module 7: Notification system.
  - Module 8: Full UI integration.
- **Phase 4: MVP Refinement (2 weeks)**:
  - Polish UI/UX, optimize performance, address feedback.
- **Phase 5: Deployment & Validation (1 week)**:
  - Deploy to production, final testing, documentation.

## 6. Constraints and Mitigations
- **Hardware (8GB RAM MacBook Pro)**:
  - Use progressive processing for transformations.
  - Leverage cloud-based tools (Supabase, Vercel) for heavy tasks.
- **Zero Budget**:
  - Use free-tier services (Supabase, Vercel, n8n).
  - Rely on open-source libraries (ImageMagick, FFmpeg).
- **Timeline (12 weeks)**:
  - Prioritize core features (text/image transformations, 2–3 platforms).
  - Defer advanced features (e.g., audio transformations) to post-MVP.

## 7. Future Considerations
- **Scalability**: Design modules for horizontal scaling (e.g., add Docker instances).
- **Extensibility**: Support new platforms and content types via API extensions.
- **AI Enhancements**: Integrate advanced NLP or generative AI for transformations.

## 8. Conclusion
This modular development plan divides ContentForge into eight independent modules, enabling parallel development by teams like Lovable (Frontend) and bolt (Backend). Clear APIs, mocking, and regular integration testing ensure seamless splicing without affecting core functionality. The plan aligns with the platform’s microservices architecture and resource constraints, delivering a functional MVP within 12 weeks.

## 9. References
- Product Description ([Product Description](attachment id:0 type:text_file filename:product_description.md))
- Project Kickoff ([Project Kickoff](attachment id:1 type:text_file filename:project_kickoff.md))