# ContentForge

## Overview

ContentForge is a digital content automation platform that streamlines the creation, transformation, management, and distribution of content across multiple platforms, including YouTube, Instagram, LinkedIn, and blogs. Designed for digital marketers, content creators, small businesses, agencies, and influencers, ContentForge enables users to input content once, automate transformations (e.g., text to video, image optimization), and publish to various platforms with minimal effort. The platform supports a modular architecture, ensuring scalability, security, and ease of use.

Built under a zero-budget constraint with development on an 8GB RAM MacBook Pro, ContentForge leverages free-tier services and open-source tools to deliver a robust Minimum Viable Product (MVP) within a 12-week timeline. The project is currently in development, with a planned completion by mid-August 2025.

### Key Features
- **Universal Input Portal**: Accepts diverse inputs (text, documents, images, spreadsheets) for multi-platform content creation.
- **Intelligent Workflow Orchestration**: Automates content transformation and publishing using n8n workflows.
- **Powerful Content Transformation Engine**: Generates platform-specific content (e.g., blog posts, videos, social media posts) using AI-driven processing.
- **Multi-Platform Publishing System**: Publishes content to YouTube, Instagram, LinkedIn, and more, with scheduling capabilities.
- **Centralized Content Management**: Organizes content with versioning, search, and team collaboration tools.
- **Scalable and Secure Infrastructure**: Built with Next.js, Supabase, Docker, and Vercel, ensuring reliability and data protection.

## Tech Stack

| **Component**          | **Technology**                     | **Purpose**                                                                 |
|------------------------|------------------------------------|-----------------------------------------------------------------------------|
| **Frontend**           | Next.js (React with TypeScript)    | Responsive, SEO-friendly UI                                                 |
| **Styling**            | Tailwind CSS, shadcn/UI            | Rapid, consistent design                                                    |
| **Backend**            | Node.js, Express.js, TypeScript    | RESTful APIs and service logic                                              |
| **Database**           | Supabase (PostgreSQL)              | Data storage with authentication and real-time features                     |
| **Workflow Automation**| n8n                                | Orchestrates content transformation and publishing                           |
| **Content Processing** | Sharp, fluent-ffmpeg, NLP tools    | Handles text, image, video, and audio transformations                        |
| **Containerization**   | Docker                             | Consistent development and production environments                           |
| **Deployment**         | Vercel (frontend), Heroku/Railway (backend) | Free-tier hosting with scalability                                          |
| **CI/CD**              | GitHub Actions                     | Automated testing and deployment                                            |
| **Testing**            | Jest, React Testing Library, Cypress | Unit, integration, and end-to-end testing                                   |
| **Security**           | Supabase Auth, JWT, HTTPS, DOMPurify | Secure authentication, authorization, and data protection                    |

## Project Structure

```
contentforge/
├── .env.example           # Sample environment variables
├── .gitignore             # Files/directories to ignore
├── .eslintrc.json         # ESLint configuration
├── .prettierrc            # Prettier configuration
├── README.md              # Project documentation
├── package.json           # Project dependencies and scripts
├── docker-compose.yml     # Docker configuration for local development
├── src/
│   ├── frontend/          # Next.js frontend
│   │   ├── pages/         # Next.js pages (e.g., index.tsx, login.tsx)
│   │   ├── components/    # Reusable React components
│   │   ├── styles/        # CSS and Tailwind styles
│   │   ├── public/        # Static assets (images, fonts)
│   │   ├── types/         # TypeScript type definitions
│   │   ├── __tests__/     # Frontend tests
│   │   └── tsconfig.json  # Frontend TypeScript config
│   ├── backend/           # Node.js/Express.js backend
│   │   ├── controllers/   # API logic
│   │   ├── models/        # Database models
│   │   ├── routes/        # API routes
│   │   ├── middleware/    # Custom middleware
│   │   ├── config/        # Configuration files
│   │   ├── types/         # TypeScript types
│   │   ├── __tests__/     # Backend tests
│   │   ├── Dockerfile     # Backend Docker config
│   │   └── tsconfig.json  # Backend TypeScript config
├── database/              # Supabase database
│   └── migrations/        # SQL migration scripts
├── workflows/             # n8n workflow files
├── tests/                 # End-to-end tests
│   └── e2e/               # E2E test scripts
└── docs/                  # Documentation
    ├── api/               # API docs
    ├── architecture/      # System architecture
    └── user_guides/       # User guides
```

## Prerequisites

- **Node.js**: Version 18.x or higher
- **Docker**: For local backend services
- **Git**: For version control
- **Supabase Account**: Free-tier account for database and authentication
- **Vercel Account**: For frontend deployment
- **Heroku/Railway Account**: For backend deployment
- **GitHub Account**: For repository access and CI/CD

## Setup Instructions

### 1. Clone the Repository
```bash
git clone https://github.com/contentforge/contentforge.git
cd contentforge
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Configure Environment Variables
Copy the `.env.example` to `.env` and fill in the required values:
```bash
cp .env.example .env
```
Example `.env`:
```
# Supabase
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key

# Backend
PORT=3001

# External APIs
YOUTUBE_API_KEY=your_youtube_api_key
INSTAGRAM_ACCESS_TOKEN=your_instagram_access_token
```

### 4. Set Up Supabase
1. Create a new Supabase project at [supabase.com](https://supabase.com).
2. Run database migrations:
   ```bash
   npx supabase db push
   ```
3. Configure authentication (email/password, social logins) in the Supabase dashboard.

### 5. Set Up n8n
1. Install n8n locally or use a free-tier cloud instance:
   ```bash
   npm install -g n8n
   n8n start
   ```
2. Import workflow templates from the `workflows/` directory via the n8n UI.

### 6. Run Locally
- **Frontend**:
  ```bash
  cd src/frontend
  npm run dev
  ```
  Access at `http://localhost:3000`.

- **Backend**:
  ```bash
  docker-compose up
  ```
  Access APIs at `http://localhost:3001`.

### 7. Run Tests
```bash
npm run test
```
Includes unit, integration, and end-to-end tests.

## Usage

1. **Access the Platform**:
   - Visit `http://localhost:3000` (local) or the Vercel deployment URL.
   - Register or log in using email/password or social logins.

2. **Create Content**:
   - Navigate to the content input portal.
   - Upload text, documents, images, or spreadsheets.
   - Select target platforms (e.g., YouTube, Instagram).

3. **Automate Workflows**:
   - Choose or customize a workflow template in the dashboard.
   - Trigger the workflow to transform and publish content.

4. **Monitor Analytics**:
   - View performance metrics (views, likes, shares) in the analytics dashboard.

5. **Manage Content**:
   - Use the content library to search, edit, or republish content.
   - Collaborate with team members via role-based access.

## Deployment

### Frontend
1. Push changes to the `main` branch.
2. Vercel automatically deploys via GitHub integration.
3. Configure environment variables in the Vercel dashboard.

### Backend
1. Build and push Docker images to Heroku/Railway:
   ```bash
   docker build -t contentforge-backend ./src/backend
   heroku container:push web
   ```
2. Set environment variables in the hosting platform’s dashboard.

## Contributing

We welcome contributions to ContentForge! Please follow these steps:

1. **Fork the Repository**:
   ```bash
   git clone https://github.com/your-username/contentforge.git
   ```

2. **Create a Feature Branch**:
   ```bash
   git checkout -b feature/your-feature
   ```

3. **Commit Changes**:
   Use [Conventional Commits](https://www.conventionalcommits.org/):
   ```bash
   git commit -m "feat: add content preview functionality"
   ```

4. **Run Tests and Linting**:
   ```bash
   npm run lint
   npm run test
   ```

5. **Push and Create a Pull Request**:
   ```bash
   git push origin feature/your-feature
   ```
   Open a pull request on GitHub with a clear description.

6. **Code Review**:
   At least one reviewer must approve the pull request before merging.

## Code of Conduct

All contributors are expected to adhere to the [Contributor Covenant Code of Conduct](https://www.contributor-covenant.org/). Treat everyone with respect and foster an inclusive environment.

## License

ContentForge is licensed under the [MIT License](LICENSE). See the LICENSE file for details.

## Contact

For questions, feedback, or support, contact the project team:
- **Email**: <EMAIL>
- **GitHub Issues**: [github.com/contentforge/contentforge/issues](https://github.com/contentforge/contentforge/issues)

## Acknowledgements

- Built with ❤️ using open-source tools and free-tier services.
- Special thanks to the Next.js, Supabase, n8n, and Vercel communities.

---

*ContentForge: Automate your content, amplify your impact.*