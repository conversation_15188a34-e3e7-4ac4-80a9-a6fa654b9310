# Frontend Guidelines for ContentForge

## 1. Introduction
ContentForge is a cloud-based digital content automation platform that streamlines the creation, management, and distribution of content across platforms like YouTube, Instagram, LinkedIn, and blogs. Its frontend, built with Next.js and styled using shadcn/UI or Tailwind CSS, delivers a responsive, Progressive Web App (PWA)-capable interface tailored for digital marketers, content creators, small businesses, agencies, and influencers. These guidelines provide a comprehensive framework for frontend developers to ensure consistency, usability, performance, and scalability in the platform’s user interface.

### Purpose
The guidelines aim to:
- Standardize development practices for a cohesive user experience.
- Ensure accessibility, performance, and security compliance.
- Facilitate collaboration through shared conventions and tools.
- Support maintainability and scalability as the platform evolves.

### Scope
This document covers design principles, UI/UX standards, technical architecture, coding practices, testing, deployment, and PWA features. It aligns with the platform’s requirements as outlined in the product description, project kickoff, PRD, SRS, and App Flow documents.

## 2. Design Principles
The frontend design adheres to the following principles:
- **Simplicity**: Clean, intuitive UI with minimal complexity.
- **Consistency**: Uniform patterns and styles across all screens.
- **Responsiveness**: Seamless functionality across devices and screen sizes.
- **Accessibility**: Compliance with WCAG 2.1 for inclusive usability.
- **Performance**: Fast load times and smooth interactions.
- **User-Centricity**: Tailored to the needs of non-technical and professional users.
- **Scalability**: Flexible design to support future features.
- **Security**: Robust measures to protect user data.

## 3. UI Components
UI components are standardized using shadcn/UI, ensuring consistency and accessibility.

### 3.1 Buttons
- **Types**: Primary (e.g., "Publish Now"), Secondary (e.g., "Cancel"), Danger (e.g., "Delete").
- **Styles**: Use shadcn/UI Button component with variants (primary, secondary, destructive).
- **Usage**:
  - Primary for main actions.
  - Secondary for less prominent actions.
  - Danger for destructive actions.
- **Accessibility**: Ensure focusable, with ARIA labels if needed.

### 3.2 Forms
- **Layout**: Vertical with labels above inputs.
- **Validation**: Inline feedback and error messages below fields.
- **Components**: Use shadcn/UI Input, Textarea, Select.
- **File Uploads**:
  - Support drag-and-drop (max 50MB).
  - Show progress indicators and error messages.
- **Accessibility**: Associate labels with fields, ensure focus management.

### 3.3 Navigation
- **Main Navigation**: Top bar with links to Dashboard, Content Library, Workflows, Analytics.
- **Mobile Navigation**: Hamburger menu for small screens.
- **Pagination**: Use "Previous" and "Next" buttons for lists.
- **Accessibility**: Keyboard-accessible with clear focus states.

### 3.4 Typography
- **Font Family**: Inter or Open Sans (sans-serif).
- **Sizes**:
  - H1: 2rem
  - H2: 1.5rem
  - Body: 1rem
  - Small: 0.875rem
- **Line Height**: 1.5x for body text.
- **Accessibility**: Minimum 4.5:1 contrast ratio.

### 3.5 Color Scheme
- **Primary**: #3B82F6 (Blue) – Buttons, accents.
- **Secondary**: #10B981 (Green) – Success states.
- **Error**: #EF4444 (Red) – Errors, danger actions.
- **Neutral**:
  - Background: #FFFFFF
  - Text: #374151
  - Secondary Text: #6B7280
- **Accessibility**: Ensure 4.5:1 contrast.

### 3.6 Icons
- **Library**: Font Awesome or Material Icons.
- **Usage**: Use sparingly, ensure scalability (1.5rem).
- **Accessibility**: Provide ARIA labels for interactive icons.

## 4. Layout and Structure
- **Grid System**: 12-column grid using Tailwind CSS.
- **Breakpoints**:
  - Mobile: <768px
  - Tablet: 768px–1024px
  - Desktop: >1024px
- **Page Templates**:
  - Dashboard: Two-column with widgets.
  - Content Creation: Single-column with input and preview.
  - Content Library: Table/grid with filters.
  - Analytics: Chart-based dashboard.
- **Responsive Design**: Mobile-first, no horizontal scrolling.

## 5. Accessibility
- **Semantic HTML**: Use `<nav>`, `<main>`, etc.
- **ARIA Attributes**: Apply for modals, dynamic content.
- **Keyboard Navigation**: Ensure all elements are accessible via Tab key.
- **Color Contrast**: Minimum 4.5:1 ratio.
- **Alt Text**: Descriptive text for images and media.
- **Testing**: Use axe or Lighthouse, test with screen readers (NVDA, VoiceOver).

## 6. Performance
- **Image Optimization**: Use Next.js Image component for lazy loading, responsive sizes.
- **Minimize HTTP Requests**: Combine CSS/JS, use CSS-in-JS.
- **Caching**: Browser caching for static assets, service workers for PWA.
- **Code Splitting**: Dynamic imports for on-demand loading.
- **Monitoring**: Use Lighthouse for metrics (e.g., First Contentful Paint <2s).

## 7. Security
- **Input Sanitization**: Use DOMPurify [DOMPurify](https://github.com/cure53/DOMPurify) to prevent XSS.
- **CSRF Protection**: Implement tokens for form submissions.
- **Secure APIs**: Use HTTPS, OAuth for third-party integrations.
- **Sensitive Data**: Store secrets in environment variables, use HTTP-only cookies.
- **Error Handling**: Avoid exposing sensitive information, log errors with Sentry [Sentry](https://sentry.io/).

## 8. Testing
- **Unit Tests**: Jest, React Testing Library for components.
- **Integration Tests**: Test component-API interactions with MSW.
- **End-to-End Tests**: Cypress for user flows.
- **Accessibility Tests**: axe for WCAG compliance.
- **Performance Tests**: Lighthouse for load times.
- **CI/CD**: Integrate tests in GitHub Actions.

## 9. Version Control and Collaboration
- **Git**:
  - Feature branches (e.g., `feature/content-upload`).
  - Pull requests with reviews.
- **Commit Messages**: Conventional commits (e.g., `feat: add content preview`).
- **Code Reviews**: Minimum one reviewer, focus on readability.
- **Tools**: GitHub Issues for tasks, Slack for communication.

## 10. Tools and Technologies
| Tool | Purpose |
|------|---------|
| Next.js | React framework for SSR, SSG, and routing |
| Tailwind CSS/shadcn/UI | Styling and component library |
| React Context | Global state management (user auth, settings) |
| Supabase Client | Real-time data and auth [Supabase](https://supabase.com/) |
| Jest, React Testing Library | Unit and integration testing |
| Cypress | End-to-end testing |
| ESLint, Prettier | Linting and formatting |
| Sentry | Error tracking |
| Storybook | Component documentation |
| Vercel | Deployment |
| Workbox | Service workers for PWA |

## 11. Coding Standards
- **Naming**:
  - Variables: camelCase (e.g., `userData`).
  - Components: PascalCase (e.g., `ContentForm`).
  - Files: kebab-case (e.g., `content-form.js`).
- **File Structure**:
  - `pages/`: Next.js pages.
  - `components/`: Reusable UI components.
  - `utils/`: Utility functions.
  - `hooks/`: Custom hooks.
- **Formatting**: Prettier with `.prettierrc`.
- **Linting**: ESLint with `.eslintrc`.
- **Comments**: JSDoc for functions and components.

## 12. Documentation
- **Component Documentation**: Use Storybook for props and examples.
- **Project Documentation**: README with setup, contribution guidelines.
- **API Documentation**: Document API routes used by frontend.
- **User Documentation**: In-app help resources (tooltips, FAQs).

## 13. Deployment
- **CI/CD**: Vercel with GitHub integration.
- **Environment Variables**: Store secrets in Vercel dashboard.
- **Monitoring**: Vercel logs, Sentry for errors.
- **Caching**: Use Vercel’s CDN for assets.
- **Rollback**: Maintain version history for quick rollbacks.

## 14. PWA Features
- **Service Workers**: Workbox for caching static assets, API responses.
- **Manifest**: `manifest.json` with app name, icons, and start URL.
- **Offline Support**: Cache Dashboard, Content Library for offline access.
- **Installability**: Ensure HTTPS, manifest, and service worker compliance.

## 15. Onboarding and Help
- **Welcome Tour**: Use Intro.js [Intro.js](https://introjs.com/) for guided tours.
- **Tooltips**: Add help icons for complex features.
- **Help Center**: Links to documentation, support tickets.
- **Feedback**: In-app form for user suggestions.