# Developer Onboarding Guide

Welcome to ContentForge! This guide will help you get started as a developer on the project.

## Project Overview

ContentForge is a digital content automation platform that streamlines the creation, transformation, management, and distribution of content across multiple platforms. It's built with Next.js, Supabase, and n8n, focusing on scalability, security, and ease of use.

## First Steps

### 1. Set Up Your Development Environment

Follow the [Setup Guide](./setup-guide.md) to configure your local development environment.

### 2. Understand the Project Structure

The project is organized as follows:

- **frontend/**: Next.js application with React and TypeScript
- **backend/**: Node.js/Express.js API server
- **docs/**: Project documentation
- **docker-compose.yml**: Docker configuration for local development

### 3. Review the Documentation

Familiarize yourself with these key documents:

- [Project README](../README.md): Overview of the project
- [Contributing Guide](./contributing.md): How to contribute to the project
- [Architecture Overview](./directory-structure.md): System architecture and design
- [API Documentation](./api-docs.md): API endpoints and usage

## Development Workflow

### 1. Pick an Issue

- Browse the [GitHub Issues](https://github.com/contentforge/contentforge/issues) to find tasks labeled "good first issue" or ask a team member for guidance.
- Assign yourself to the issue you want to work on.

### 2. Create a Branch

Create a new branch for your work:

```bash
git checkout -b feature/your-feature-name
```

Use prefixes like `feature/`, `bugfix/`, `docs/`, etc., to categorize your branch.

### 3. Develop and Test

- Write code following the project's coding standards.
- Add tests for new features or bug fixes.
- Run tests and linting locally before committing:
  ```bash
  npm run lint
  npm test
  ```

### 4. Submit a Pull Request

- Push your branch to your fork:
  ```bash
  git push origin feature/your-feature-name
  ```
- Create a pull request to the `main` branch of the original repository.
- Fill out the pull request template with details about your changes.

### 5. Code Review

- Address any feedback from reviewers.
- Make necessary changes and push updates to your branch.
- Once approved, your pull request will be merged.

## Key Technologies

### Frontend

- **Next.js**: React framework for server-rendered applications
- **TypeScript**: Typed JavaScript for better developer experience
- **Tailwind CSS**: Utility-first CSS framework
- **Supabase Client**: Database and authentication client

### Backend

- **Node.js**: JavaScript runtime
- **Express.js**: Web framework
- **TypeScript**: Type safety for JavaScript
- **Supabase**: PostgreSQL database with authentication

### DevOps

- **Docker**: Containerization
- **GitHub Actions**: CI/CD
- **Vercel**: Frontend deployment
- **Heroku/Railway**: Backend deployment

## Communication Channels

- **GitHub Issues**: For bug reports and feature requests
- **Pull Requests**: For code reviews and discussions
- **Team Chat**: For day-to-day communication (ask for an invitation)

## Learning Resources

### Next.js and React

- [Next.js Documentation](https://nextjs.org/docs)
- [React Documentation](https://reactjs.org/docs/getting-started.html)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/handbook/intro.html)

### Supabase

- [Supabase Documentation](https://supabase.io/docs)
- [Supabase JavaScript Client](https://supabase.io/docs/reference/javascript/supabase-client)

### n8n

- [n8n Documentation](https://docs.n8n.io/)
- [n8n Workflows](https://docs.n8n.io/workflows/)

## Getting Help

If you're stuck or have questions:

1. Check the documentation and troubleshooting guide.
2. Search GitHub issues for similar problems.
3. Ask in the team chat.
4. Reach out to your assigned mentor or team lead.

Welcome aboard, and happy coding!
