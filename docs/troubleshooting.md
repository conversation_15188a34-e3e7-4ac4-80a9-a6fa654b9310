# Troubleshooting Guide

This guide provides solutions for common issues you might encounter while working with ContentForge.

## Table of Contents

1. [Development Environment Issues](#development-environment-issues)
2. [Frontend Issues](#frontend-issues)
3. [Backend Issues](#backend-issues)
4. [Docker Issues](#docker-issues)
5. [Database Issues](#database-issues)
6. [n8n Workflow Issues](#n8n-workflow-issues)

## Development Environment Issues

### Node.js Version Problems

**Issue**: Errors related to unsupported JavaScript features or incompatible dependencies.

**Solution**:
- Ensure you're using Node.js 18.x or higher:
  ```bash
  node --version
  ```
- If needed, install the correct version using nvm:
  ```bash
  nvm install 18
  nvm use 18
  ```

### Package Installation Failures

**Issue**: `npm install` fails with dependency errors.

**Solution**:
- Clear npm cache:
  ```bash
  npm cache clean --force
  ```
- Delete node_modules and package-lock.json:
  ```bash
  rm -rf node_modules package-lock.json
  npm install
  ```
- Check for conflicting dependencies in package.json.

## Frontend Issues

### Next.js Build Errors

**Issue**: Errors when building the Next.js application.

**Solution**:
- Check for TypeScript errors:
  ```bash
  cd frontend
  npx tsc --noEmit
  ```
- Ensure all required environment variables are set.
- Check for outdated dependencies:
  ```bash
  npm outdated
  ```

### API Connection Issues

**Issue**: Frontend can't connect to backend API.

**Solution**:
- Verify the backend is running.
- Check API URL configuration in the frontend.
- Ensure CORS is properly configured on the backend.
- Check network tab in browser DevTools for specific errors.

## Backend Issues

### Server Won't Start

**Issue**: Backend server fails to start.

**Solution**:
- Check for port conflicts:
  ```bash
  lsof -i :4000
  ```
- Ensure all required environment variables are set.
- Check for syntax errors in the code.
- Look for detailed error messages in the console.

### Database Connection Issues

**Issue**: Backend can't connect to the database.

**Solution**:
- Verify database credentials in environment variables.
- Check if the database server is running.
- Test connection with a database client.
- Check firewall settings.

## Docker Issues

### Container Build Failures

**Issue**: Docker container fails to build.

**Solution**:
- Check Dockerfile syntax.
- Ensure all required files are present.
- Look for specific error messages in the build output.
- Try rebuilding with no cache:
  ```bash
  docker-compose build --no-cache
  ```

### Container Runtime Issues

**Issue**: Docker containers exit unexpectedly or fail to start.

**Solution**:
- Check container logs:
  ```bash
  docker-compose logs
  ```
- Verify port mappings and environment variables in docker-compose.yml.
- Ensure volumes are properly configured.
- Check for resource constraints (memory, CPU).

## Database Issues

### Migration Failures

**Issue**: Database migrations fail to apply.

**Solution**:
- Check migration scripts for errors.
- Verify database user permissions.
- Look for detailed error messages in the logs.
- Consider rolling back to a previous migration.

### Performance Issues

**Issue**: Slow database queries.

**Solution**:
- Check for missing indexes.
- Optimize query patterns.
- Consider adding caching.
- Monitor database resource usage.

## n8n Workflow Issues

### Workflow Execution Failures

**Issue**: n8n workflows fail to execute properly.

**Solution**:
- Check workflow configuration in n8n UI.
- Verify credentials for external services.
- Look for error messages in the n8n execution log.
- Test individual nodes to isolate the issue.

### Connection Issues

**Issue**: n8n can't connect to other services.

**Solution**:
- Check network connectivity.
- Verify API keys and credentials.
- Ensure services are running and accessible.
- Check for rate limiting or IP restrictions.

## Getting Additional Help

If you encounter an issue not covered in this guide:

1. Search the project's GitHub issues.
2. Check the project documentation.
3. Ask for help in the project's communication channels.
4. Create a new issue with detailed information about your problem, including:
   - Steps to reproduce
   - Expected behavior
   - Actual behavior
   - Error messages
   - Environment details (OS, Node.js version, etc.)
