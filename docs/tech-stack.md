# Tech Stack Document for ContentForge

## 1. Introduction

This document outlines the technology stack for ContentForge, a platform that automates content creation, transformation, and multi-platform publishing. Built with a zero-budget constraint, a tight 12-week timeline, and limited hardware (8GB RAM MacBook Pro), the stack prioritizes free-tier services, open-source tools, and efficient technologies to deliver a scalable, secure, and modular Minimum Viable Product (MVP). The tech stack is divided into frontend, backend, infrastructure, development tools, and security components to ensure a cohesive development process.

---

## 2. Frontend

The frontend provides a responsive, intuitive interface for content input, workflow management, and analytics tracking, optimized for performance and accessibility across devices.

- **Framework**: 
  - **Next.js (React with TypeScript)**: A React framework with server-side rendering (SSR) and static site generation (SSG) for fast performance and SEO. TypeScript enhances type safety and maintainability.

- **Styling**:
  - **Tailwind CSS**: A utility-first CSS framework for rapid, responsive design development.
  - **shadcn/UI**: Reusable, accessible React components built with Tailwind CSS to accelerate UI development.

- **State Management**:
  - **React Context API** or **Zustand**: Manages global state (e.g., user sessions, content data). Zustand offers lightweight, efficient state management for complex scenarios.

- **Data Visualization**:
  - **Chart.js**: A simple library for rendering interactive charts to display content performance analytics.

- **Testing**:
  - **Jest**: Unit testing for React components and utilities.
  - **React Testing Library**: Tests user interactions and component behavior.
  - **Cypress**: End-to-end testing for critical workflows like content creation and publishing.

---

## 3. Backend

The backend employs a microservices architecture for modularity and scalability, with containerized services to ensure consistency across environments.

- **Language**:
  - **Node.js with TypeScript**: Aligns with the frontend, supports asynchronous operations, and offers libraries for content processing and APIs.

- **Framework**:
  - **Express.js**: A lightweight framework for building RESTful APIs and managing backend routes.

- **Database**:
  - **Supabase (PostgreSQL)**: An open-source platform with a scalable PostgreSQL database, real-time features, and authentication, handling user data, content, workflows, and analytics.

- **Authentication**:
  - **Supabase Auth**: Provides user authentication (email/password, social logins) with JWT tokens for secure API access.

- **Workflow Orchestration**:
  - **n8n**: An open-source tool for automating workflows, managing content transformation and publishing tasks with visual workflow design.

- **Content Transformation**:
  - **Text Processing**: 
    - **Natural** or **Compromise**: JavaScript NLP libraries for basic text tasks (e.g., tokenization, keyword extraction).
  - **Image Processing**:
    - **Sharp**: A high-performance library for resizing, cropping, and formatting images.
  - **Video Processing**:
    - **fluent-ffmpeg**: A Node.js wrapper for FFmpeg, enabling video trimming, conversion, and editing.
  - **Audio Processing**:
    - **audiobuffer-to-wav**: Converts audio buffers to WAV files for basic audio transformations.

- **Task Queuing**:
  - **Database-based queue**: A "tasks" table in Supabase manages transformation jobs sequentially, accommodating hardware limitations.

- **Integrations**:
  - **Platform APIs**: Connects to YouTube, Instagram, LinkedIn, etc., using OAuth for authentication and API calls for publishing and analytics retrieval.

---

## 4. Infrastructure

The infrastructure leverages free-tier platforms and containerization to support development, testing, and deployment within budget constraints.

- **Containerization**:
  - **Docker**: Containerizes backend services for consistent development and production environments.

- **Deployment**:
  - **Frontend**: 
    - **Vercel**: Hosts Next.js apps with a free tier, offering fast deployments and serverless functions.
  - **Backend Services**:
    - **Heroku** or **Railway**: Free-tier platforms for deploying Dockerized Node.js services with basic scaling capabilities.
  - **Database**:
    - **Supabase**: Hosts the PostgreSQL database with free-tier limits, including automated backups and real-time features.

- **CI/CD**:
  - **GitHub Actions**: Automates testing, linting, and deployment pipelines for efficient releases.

- **Monitoring**:
  - **Sentry**: Free-tier error tracking for real-time crash monitoring.
  - **Vercel Analytics**: Tracks frontend performance and user engagement.

---

## 5. Development Tools

These tools streamline development, collaboration, and local testing.

- **Version Control**:
  - **Git**: Manages source code versioning.
  - **GitHub**: Hosts repositories, issues, and pull requests.

- **IDE**:
  - **VS Code**: A customizable editor with TypeScript, Docker, and Git support.

- **Local Development**:
  - **Docker Compose**: Runs backend services and dependencies locally.
  - **Supabase CLI**: Manages local database setup and migrations (if available).

---

## 6. Security

Security measures protect user data and ensure safe interactions with external platforms.

- **Authentication**:
  - **JWT**: Supabase Auth generates tokens for secure, stateless API authentication.

- **Authorization**:
  - **Role-Based Access Control (RBAC)**: Restricts access based on user roles (e.g., admin, editor).

- **Data Encryption**:
  - **HTTPS**: Encrypts data in transit.
  - **Supabase Database Encryption**: Secures data at rest.

- **Input Validation**:
  - **DOMPurify**: Sanitizes inputs to prevent XSS and injection attacks.

---

## 7. Summary of Tech Stack

| **Component**                | **Technology**                     | **Purpose**                                                                 |
|------------------------------|------------------------------------|-----------------------------------------------------------------------------|
| **Frontend Framework**       | Next.js (React with TypeScript)    | Responsive, SEO-friendly UI development                                     |
| **Styling**                  | Tailwind CSS, shadcn/UI            | Fast, consistent, responsive design                                         |
| **State Management**         | React Context API or Zustand       | Efficient global state management                                           |
| **Data Visualization**       | Chart.js                           | Interactive analytics charts                                                |
| **Backend Language**         | Node.js with TypeScript            | Scalable, asynchronous service development                                  |
| **API Framework**            | Express.js                         | RESTful API routing and request handling                                    |
| **Database**                 | Supabase (PostgreSQL)              | Data storage with real-time and authentication features                     |
| **Authentication**           | Supabase Auth                      | Secure user authentication with JWT                                         |
| **Workflow Orchestration**   | n8n                                | Automate content workflows                                                  |
| **Content Transformation**   | Sharp, fluent-ffmpeg, etc.         | Process text, images, videos, and audio                                     |
| **Task Queuing**             | Database-based queue               | Sequential task management on limited hardware                              |
| **Integrations**             | Platform APIs (OAuth)              | Multi-platform publishing and analytics                                     |
| **Containerization**         | Docker                             | Consistent service environments                                             |
| **Deployment (Frontend)**    | Vercel                             | Host frontend with serverless capabilities                                  |
| **Deployment (Backend)**     | Heroku or Railway                  | Deploy backend services with free-tier support                              |
| **CI/CD**                    | GitHub Actions                     | Automate testing and deployment                                             |
| **Monitoring**               | Sentry, Vercel Analytics           | Error tracking and performance monitoring                                   |
| **Version Control**          | Git, GitHub                        | Code management and collaboration                                           |
| **Security**                 | JWT, RBAC, HTTPS, DOMPurify        | Secure authentication, authorization, and data protection                   |

---

## 8. Conclusion

The ContentForge tech stack balances functionality, performance, and cost within strict constraints. Free-tier services (Supabase, Vercel, Heroku/Railway) and open-source tools (n8n, Docker) enable a robust MVP, while TypeScript and microservices ensure maintainability and scalability. This foundation supports core features—content input, transformation, and publishing—while allowing for future enhancements as resources grow.