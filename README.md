# ContentForge

ContentForge is a digital content automation platform that streamlines creation, transformation, and multi-platform publishing (YouTube, Instagram, LinkedIn). Built with Next.js, Supabase, and n8n, it offers AI-driven workflows, analytics, and a scalable, secure MVP. Developed on a zero-budget with an 8GB RAM MacBook Pro in 12 weeks.

## Features

- **Universal Input Portal**: Accept diverse content inputs (text, images, video)
- **Intelligent Workflow Orchestration**: Automate content transformation and publishing
- **Multi-Platform Publishing**: Publish to YouTube, Instagram, LinkedIn, and more
- **Centralized Content Management**: Organize and track all your content
- **Analytics Dashboard**: Monitor performance across platforms

## Tech Stack

- **Frontend**: Next.js with TypeScript and Tailwind CSS
- **Backend**: Node.js/Express.js
- **Database**: Supabase (PostgreSQL)
- **Workflow Automation**: n8n
- **Deployment**: Vercel (frontend), Docker (backend)

## Getting Started

### Prerequisites

- Node.js 18.x or higher
- npm or yarn
- Docker and Docker Compose (for local development)

### Installation

```bash
# Clone the repository
git clone https://github.com/your-username/contentforge.git
cd contentforge

# Install dependencies
npm install

# Start development servers
docker-compose up
```

For detailed setup instructions, see the [Setup Guide](./docs/setup-guide.md).

## Documentation

- [Setup Guide](./docs/setup-guide.md)
- [Contributing Guide](./docs/contributing.md)
- [Troubleshooting](./docs/troubleshooting.md)
- [Developer Onboarding](./docs/onboarding.md)

## Development

```bash
# Run frontend development server
npm run dev

# Run linting
npm run lint

# Run tests
npm test
```

## Deployment

The application can be deployed using Docker and Vercel. See the [deployment documentation](./docs/readme.md#deployment) for details.

## Contributing

Contributions are welcome! Please see our [Contributing Guide](./docs/contributing.md) for details.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
