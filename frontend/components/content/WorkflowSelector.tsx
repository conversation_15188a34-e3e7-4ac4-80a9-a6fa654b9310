"use client";

import { useState } from "react";

interface WorkflowSelectorProps {
  selectedWorkflow: string;
  onChange: (workflow: string) => void;
}

interface Workflow {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  platforms: string[];
}

export default function WorkflowSelector({
  selectedWorkflow,
  onChange,
}: WorkflowSelectorProps) {
  const [showCustom, setShowCustom] = useState(false);

  // Mock workflows
  const workflows: Workflow[] = [
    {
      id: "social-media-blitz",
      name: "Social Media Blitz",
      description: "Optimize content for multiple social platforms simultaneously",
      platforms: ["instagram", "twitter", "facebook", "linkedin"],
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-6 w-6"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M13 10V3L4 14h7v7l9-11h-7z"
          />
        </svg>
      ),
    },
    {
      id: "blog-post-generator",
      name: "Blog Post Generator",
      description: "Transform content into SEO-optimized blog posts",
      platforms: ["blog"],
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-6 w-6"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"
          />
        </svg>
      ),
    },
    {
      id: "video-content-suite",
      name: "Video Content Suite",
      description: "Create and optimize video content for YouTube and social media",
      platforms: ["youtube", "instagram", "facebook"],
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-6 w-6"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
          />
        </svg>
      ),
    },
    {
      id: "professional-linkedin",
      name: "Professional LinkedIn",
      description: "Optimize content for professional audience on LinkedIn",
      platforms: ["linkedin"],
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-6 w-6"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
          />
        </svg>
      ),
    },
    {
      id: "custom",
      name: "Custom Workflow",
      description: "Create a custom workflow for your specific needs",
      platforms: [],
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-6 w-6"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 6v6m0 0v6m0-6h6m-6 0H6"
          />
        </svg>
      ),
    },
  ];

  const handleWorkflowSelect = (workflowId: string) => {
    onChange(workflowId);
    setShowCustom(workflowId === "custom");
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {workflows.map((workflow) => (
          <div
            key={workflow.id}
            className={`border rounded-lg p-4 cursor-pointer transition-colors ${
              selectedWorkflow === workflow.id
                ? "border-primary bg-primary/5"
                : "border-border hover:border-primary/50"
            }`}
            onClick={() => handleWorkflowSelect(workflow.id)}
          >
            <div className="flex items-start space-x-4">
              <div className="rounded-full bg-muted/50 p-2 text-primary">
                {workflow.icon}
              </div>
              <div>
                <h3 className="font-medium">{workflow.name}</h3>
                <p className="text-sm text-muted-foreground">
                  {workflow.description}
                </p>
                {workflow.platforms.length > 0 && (
                  <div className="flex flex-wrap gap-1 mt-2">
                    {workflow.platforms.map((platform) => (
                      <span
                        key={platform}
                        className="px-2 py-1 bg-muted rounded-full text-xs"
                      >
                        {platform}
                      </span>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {showCustom && (
        <div className="border rounded-lg p-6 mt-4">
          <h3 className="font-medium mb-4">Custom Workflow Builder</h3>
          <p className="text-sm text-muted-foreground mb-4">
            Create a custom workflow by selecting and ordering the processing steps below.
          </p>
          
          <div className="space-y-4">
            <div className="p-4 border rounded-md bg-muted/30">
              <p className="text-center text-muted-foreground">
                Custom workflow builder will be available in the next release.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
