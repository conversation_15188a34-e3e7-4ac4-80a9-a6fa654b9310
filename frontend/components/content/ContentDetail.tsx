"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { Button } from "../ui/Button";
import { <PERSON>, CardHeader, CardT<PERSON><PERSON>, CardContent, CardFooter } from "../ui/Card";
import { Skeleton } from "../ui/Skeleton";
import { useContent } from "../../context/ContentContext";
import { useToast } from "../../context/ToastContext";
import { formatDate } from "../../lib/utils";

interface ContentDetailProps {
  id: string;
}

export default function ContentDetail({ id }: ContentDetailProps) {
  const router = useRouter();
  const { contentItems, isLoading, error, fetchContentItems, deleteContent } = useContent();
  const { showToast } = useToast();
  const [isDeleting, setIsDeleting] = useState(false);
  const [contentItem, setContentItem] = useState<any>(null);

  useEffect(() => {
    fetchContentItems();
  }, [fetchContentItems]);

  useEffect(() => {
    if (contentItems.length > 0) {
      const item = contentItems.find(item => item.id === id);
      if (item) {
        setContentItem(item);
      }
    }
  }, [contentItems, id]);

  const handleDelete = async () => {
    if (confirm("Are you sure you want to delete this content? This action cannot be undone.")) {
      setIsDeleting(true);
      const { error } = await deleteContent(id);
      
      if (error) {
        showToast("Error deleting content", error, "error");
      } else {
        showToast("Content deleted", "The content has been successfully deleted.", "success");
        router.push("/content");
      }
      
      setIsDeleting(false);
    }
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "published":
        return (
          <span className="px-2 py-1 rounded-full text-xs font-medium bg-secondary/20 text-secondary">
            Published
          </span>
        );
      case "scheduled":
        return (
          <span className="px-2 py-1 rounded-full text-xs font-medium bg-primary/20 text-primary">
            Scheduled
          </span>
        );
      case "draft":
        return (
          <span className="px-2 py-1 rounded-full text-xs font-medium bg-muted text-muted-foreground">
            Draft
          </span>
        );
      default:
        return (
          <span className="px-2 py-1 rounded-full text-xs font-medium bg-muted text-muted-foreground">
            {status}
          </span>
        );
    }
  };

  // Get platform badge
  const getPlatformBadge = (platform: string) => {
    const baseClasses = "px-2 py-1 rounded-full text-xs font-medium";
    
    switch (platform.toLowerCase()) {
      case "instagram":
        return <span className={`${baseClasses} bg-pink-100 text-pink-800`}>{platform}</span>;
      case "youtube":
        return <span className={`${baseClasses} bg-red-100 text-red-800`}>{platform}</span>;
      case "linkedin":
        return <span className={`${baseClasses} bg-blue-100 text-blue-800`}>{platform}</span>;
      case "twitter":
        return <span className={`${baseClasses} bg-blue-100 text-blue-800`}>{platform}</span>;
      case "facebook":
        return <span className={`${baseClasses} bg-blue-100 text-blue-800`}>{platform}</span>;
      case "blog":
        return <span className={`${baseClasses} bg-green-100 text-green-800`}>{platform}</span>;
      default:
        return <span className={`${baseClasses} bg-gray-100 text-gray-800`}>{platform}</span>;
    }
  };

  // Get workflow name
  const getWorkflowName = (workflowId: string): string => {
    const workflowMap: Record<string, string> = {
      "social-media-blitz": "Social Media Blitz",
      "blog-post-generator": "Blog Post Generator",
      "video-content-suite": "Video Content Suite",
      "professional-linkedin": "Professional LinkedIn",
      "custom": "Custom Workflow",
    };
    return workflowMap[workflowId] || workflowId;
  };

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="p-6 border border-error/50 bg-error/10 rounded-lg text-error">
          <h3 className="font-medium mb-2">Error loading content</h3>
          <p>{error}</p>
          <Button 
            onClick={() => fetchContentItems()} 
            variant="outline" 
            className="mt-4"
          >
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  if (isLoading || !contentItem) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-between mb-8">
          <Skeleton className="h-8 w-64" />
          <Skeleton className="h-10 w-24" />
        </div>
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <Skeleton className="h-8 w-1/3" />
              <Skeleton className="h-6 w-20" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
              <div className="flex flex-wrap gap-2 my-4">
                <Skeleton className="h-6 w-20 rounded-full" />
                <Skeleton className="h-6 w-20 rounded-full" />
                <Skeleton className="h-6 w-20 rounded-full" />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
                <Skeleton className="h-20 w-full" />
                <Skeleton className="h-20 w-full" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center">
          <Button 
            variant="outline" 
            size="sm" 
            className="mr-4"
            onClick={() => router.back()}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 mr-2"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M10 19l-7-7m0 0l7-7m-7 7h18"
              />
            </svg>
            Back
          </Button>
          <h1 className="text-3xl font-bold">Content Details</h1>
        </div>
        <div className="flex space-x-2">
          <Button 
            variant="outline" 
            asChild
          >
            <Link href={`/content/${id}/edit`}>Edit Content</Link>
          </Button>
          <Button 
            variant="destructive" 
            onClick={handleDelete}
            disabled={isDeleting}
          >
            {isDeleting ? "Deleting..." : "Delete"}
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader className="pb-2">
          <div className="flex justify-between items-center">
            <CardTitle className="text-2xl">{contentItem.title}</CardTitle>
            {getStatusBadge(contentItem.status)}
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-2">Description</h3>
              <p className="text-muted-foreground">{contentItem.description}</p>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2">Platforms</h3>
              <div className="flex flex-wrap gap-2">
                {contentItem.platforms.map((platform: string) => (
                  <div key={platform}>
                    {getPlatformBadge(platform)}
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2">Workflow</h3>
              <p className="text-muted-foreground">{getWorkflowName(contentItem.workflow_id)}</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t">
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">Content Type</h3>
                <p className="capitalize">{contentItem.content_type}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">Created</h3>
                <p>{formatDate(contentItem.created_at)}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">Last Updated</h3>
                <p>{formatDate(contentItem.updated_at)}</p>
              </div>
              {contentItem.status === "scheduled" && contentItem.scheduled_for && (
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">Scheduled For</h3>
                  <p>{formatDate(contentItem.scheduled_for)}</p>
                </div>
              )}
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between border-t pt-6">
          <Button 
            variant="outline" 
            onClick={() => router.push("/content")}
          >
            Back to Content Library
          </Button>
          <div className="flex space-x-2">
            {contentItem.status === "draft" && (
              <Button>
                Publish Now
              </Button>
            )}
            {contentItem.status === "draft" && (
              <Button variant="outline">
                Schedule
              </Button>
            )}
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
