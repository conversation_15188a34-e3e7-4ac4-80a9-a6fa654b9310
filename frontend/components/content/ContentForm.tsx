"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle, CardDescription, CardContent, CardFooter } from "../ui/Card";
import { Input } from "../ui/Input";
import { Button } from "../ui/Button";
import FileUpload from "./FileUpload";
import PlatformSelector from "./PlatformSelector";
import WorkflowSelector from "./WorkflowSelector";
import ContentPreview from "./ContentPreview";
import { useContent } from "../../context/ContentContext";
import { useToast } from "../../context/ToastContext";

type InputType = "text" | "document" | "image" | "spreadsheet" | "combined";

export default function ContentForm() {
  const router = useRouter();
  const { createContent } = useContent();
  const { showToast } = useToast();

  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [inputType, setInputType] = useState<InputType>("text");
  const [textContent, setTextContent] = useState("");
  const [files, setFiles] = useState<File[]>([]);
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>([]);
  const [selectedWorkflow, setSelectedWorkflow] = useState("");
  const [step, setStep] = useState(1);
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleFileUpload = (uploadedFiles: File[]) => {
    setFiles([...files, ...uploadedFiles]);
  };

  const handleRemoveFile = (index: number) => {
    const newFiles = [...files];
    newFiles.splice(index, 1);
    setFiles(newFiles);
  };

  const handlePlatformChange = (platforms: string[]) => {
    setSelectedPlatforms(platforms);
  };

  const handleWorkflowChange = (workflow: string) => {
    setSelectedWorkflow(workflow);
  };

  const handleNextStep = () => {
    setStep(step + 1);
  };

  const handlePreviousStep = () => {
    setStep(step - 1);
  };

  const togglePreview = () => {
    setIsPreviewMode(!isPreviewMode);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Create content object
      const contentData = {
        title,
        description,
        content_type: inputType,
        status: "draft", // Default to draft
        platforms: selectedPlatforms,
        workflow_id: selectedWorkflow,
      };

      const { data, error } = await createContent(contentData);

      if (error) {
        showToast("Error creating content", error, "error");
      } else {
        showToast(
          "Content created successfully",
          "Your content has been saved as a draft.",
          "success"
        );

        // Redirect to content library
        router.push("/content");
      }
    } catch (err: any) {
      showToast("Error creating content", err.message || "An unexpected error occurred", "error");
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStepContent = () => {
    switch (step) {
      case 1:
        return (
          <>
            <CardHeader>
              <CardTitle>Create New Content</CardTitle>
              <CardDescription>
                Enter basic information about your content
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Input
                id="title"
                label="Content Title"
                placeholder="Enter a title for your content"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                required
              />
              <div className="space-y-2">
                <label htmlFor="description" className="block text-sm font-medium">
                  Description
                </label>
                <textarea
                  id="description"
                  className="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  rows={3}
                  placeholder="Enter a brief description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <label className="block text-sm font-medium">Input Type</label>
                <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
                  {["text", "document", "image", "spreadsheet", "combined"].map((type) => (
                    <button
                      key={type}
                      type="button"
                      className={`px-3 py-2 rounded-md text-sm ${
                        inputType === type
                          ? "bg-primary text-primary-foreground"
                          : "bg-muted hover:bg-muted/80"
                      }`}
                      onClick={() => setInputType(type as InputType)}
                    >
                      {type.charAt(0).toUpperCase() + type.slice(1)}
                    </button>
                  ))}
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-end">
              <Button onClick={handleNextStep}>Next: Add Content</Button>
            </CardFooter>
          </>
        );
      case 2:
        return (
          <>
            <CardHeader>
              <CardTitle>Add Your Content</CardTitle>
              <CardDescription>
                {inputType === "text"
                  ? "Enter your text content"
                  : `Upload your ${inputType} files`}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {inputType === "text" ? (
                <div className="space-y-2">
                  <label htmlFor="textContent" className="block text-sm font-medium">
                    Text Content
                  </label>
                  <textarea
                    id="textContent"
                    className="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    rows={10}
                    placeholder="Enter your content here..."
                    value={textContent}
                    onChange={(e) => setTextContent(e.target.value)}
                  />
                </div>
              ) : (
                <FileUpload
                  onFileUpload={handleFileUpload}
                  onRemoveFile={handleRemoveFile}
                  files={files}
                  acceptedFileTypes={
                    inputType === "document"
                      ? ".pdf,.doc,.docx,.txt"
                      : inputType === "image"
                      ? ".jpg,.jpeg,.png,.gif"
                      : inputType === "spreadsheet"
                      ? ".xls,.xlsx,.csv"
                      : undefined
                  }
                  maxFileSize={50 * 1024 * 1024} // 50MB
                />
              )}
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" onClick={handlePreviousStep}>
                Back
              </Button>
              <Button onClick={handleNextStep}>Next: Select Platforms</Button>
            </CardFooter>
          </>
        );
      case 3:
        return (
          <>
            <CardHeader>
              <CardTitle>Select Target Platforms</CardTitle>
              <CardDescription>
                Choose where you want to publish your content
              </CardDescription>
            </CardHeader>
            <CardContent>
              <PlatformSelector
                selectedPlatforms={selectedPlatforms}
                onChange={handlePlatformChange}
              />
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" onClick={handlePreviousStep}>
                Back
              </Button>
              <Button onClick={handleNextStep} disabled={selectedPlatforms.length === 0}>
                Next: Choose Workflow
              </Button>
            </CardFooter>
          </>
        );
      case 4:
        return (
          <>
            <CardHeader>
              <CardTitle>Choose Workflow</CardTitle>
              <CardDescription>
                Select a workflow to process your content
              </CardDescription>
            </CardHeader>
            <CardContent>
              <WorkflowSelector
                selectedWorkflow={selectedWorkflow}
                onChange={handleWorkflowChange}
              />
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" onClick={handlePreviousStep}>
                Back
              </Button>
              <Button onClick={handleNextStep} disabled={!selectedWorkflow}>
                Next: Preview
              </Button>
            </CardFooter>
          </>
        );
      case 5:
        return (
          <>
            <CardHeader>
              <CardTitle>Preview & Publish</CardTitle>
              <CardDescription>
                Review your content before publishing
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ContentPreview
                title={title}
                description={description}
                content={textContent}
                files={files}
                platforms={selectedPlatforms}
                workflow={selectedWorkflow}
              />
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" onClick={handlePreviousStep}>
                Back
              </Button>
              <div className="space-x-2">
                <Button variant="outline" onClick={togglePreview}>
                  {isPreviewMode ? "Edit" : "Preview"}
                </Button>
                <Button onClick={handleSubmit} disabled={isSubmitting}>
                  {isSubmitting ? "Publishing..." : "Save as Draft"}
                </Button>
              </div>
            </CardFooter>
          </>
        );
      default:
        return null;
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <Card className="max-w-4xl mx-auto">
        <form onSubmit={handleSubmit}>{renderStepContent()}</form>
      </Card>
    </div>
  );
}
