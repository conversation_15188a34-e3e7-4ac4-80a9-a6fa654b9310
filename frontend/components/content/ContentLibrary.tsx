"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { Button } from "../ui/Button";
import { <PERSON>, CardH<PERSON>er, CardTitle, CardContent } from "../ui/Card";
import { Skeleton } from "../ui/Skeleton";
import { useContent, ContentItem } from "../../context/ContentContext";
import { useToast } from "../../context/ToastContext";
import { formatDate } from "../../lib/utils";

export default function ContentLibrary() {
  const { contentItems, isLoading, error, fetchContentItems, deleteContent } = useContent();
  const { showToast } = useToast();
  const [filter, setFilter] = useState<string>("all");
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [isDeleting, setIsDeleting] = useState<string | null>(null);

  useEffect(() => {
    fetchContentItems();
  }, [fetchContentItems]);

  const handleDelete = async (id: string) => {
    if (confirm("Are you sure you want to delete this content? This action cannot be undone.")) {
      setIsDeleting(id);
      const { error } = await deleteContent(id);
      
      if (error) {
        showToast("Error deleting content", error, "error");
      } else {
        showToast("Content deleted", "The content has been successfully deleted.", "success");
      }
      
      setIsDeleting(null);
    }
  };

  // Filter and search content items
  const filteredItems = contentItems.filter(item => {
    const matchesFilter = filter === "all" || item.status === filter;
    const matchesSearch = searchTerm === "" || 
      item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesFilter && matchesSearch;
  });

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "published":
        return (
          <span className="px-2 py-1 rounded-full text-xs font-medium bg-secondary/20 text-secondary">
            Published
          </span>
        );
      case "scheduled":
        return (
          <span className="px-2 py-1 rounded-full text-xs font-medium bg-primary/20 text-primary">
            Scheduled
          </span>
        );
      case "draft":
        return (
          <span className="px-2 py-1 rounded-full text-xs font-medium bg-muted text-muted-foreground">
            Draft
          </span>
        );
      default:
        return (
          <span className="px-2 py-1 rounded-full text-xs font-medium bg-muted text-muted-foreground">
            {status}
          </span>
        );
    }
  };

  if (error) {
    return (
      <div className="p-6 border border-error/50 bg-error/10 rounded-lg text-error">
        <h3 className="font-medium mb-2">Error loading content</h3>
        <p>{error}</p>
        <Button 
          onClick={() => fetchContentItems()} 
          variant="outline" 
          className="mt-4"
        >
          Try Again
        </Button>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        {[1, 2, 3].map((i) => (
          <Card key={i}>
            <CardHeader className="pb-2">
              <div className="flex justify-between items-center">
                <Skeleton className="h-6 w-1/3" />
                <Skeleton className="h-6 w-20" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-2/3" />
                <div className="flex justify-between items-center mt-4">
                  <Skeleton className="h-4 w-1/4" />
                  <Skeleton className="h-8 w-20" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (contentItems.length === 0) {
    return (
      <div className="p-12 border border-dashed rounded-lg flex flex-col items-center justify-center text-center">
        <h2 className="text-xl font-semibold mb-2">No Content Yet</h2>
        <p className="text-muted-foreground max-w-md mb-6">
          You haven't created any content yet. Get started by creating your first piece of content.
        </p>
        <Button asChild>
          <Link href="/content/new">Create Your First Content</Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Filters and Search */}
      <div className="flex flex-col md:flex-row justify-between gap-4">
        <div className="flex space-x-2">
          <Button 
            variant={filter === "all" ? "default" : "outline"} 
            size="sm"
            onClick={() => setFilter("all")}
          >
            All
          </Button>
          <Button 
            variant={filter === "published" ? "default" : "outline"} 
            size="sm"
            onClick={() => setFilter("published")}
          >
            Published
          </Button>
          <Button 
            variant={filter === "scheduled" ? "default" : "outline"} 
            size="sm"
            onClick={() => setFilter("scheduled")}
          >
            Scheduled
          </Button>
          <Button 
            variant={filter === "draft" ? "default" : "outline"} 
            size="sm"
            onClick={() => setFilter("draft")}
          >
            Drafts
          </Button>
        </div>
        <div className="relative">
          <input
            type="text"
            placeholder="Search content..."
            className="w-full md:w-64 px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          {searchTerm && (
            <button
              className="absolute right-2 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
              onClick={() => setSearchTerm("")}
              aria-label="Clear search"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          )}
        </div>
      </div>

      {/* Content Items */}
      {filteredItems.length === 0 ? (
        <div className="p-8 border border-dashed rounded-lg text-center">
          <p className="text-muted-foreground">No content matches your filters.</p>
        </div>
      ) : (
        <div className="space-y-4">
          {filteredItems.map((item) => (
            <Card key={item.id}>
              <CardHeader className="pb-2">
                <div className="flex justify-between items-center">
                  <CardTitle className="text-xl">{item.title}</CardTitle>
                  {getStatusBadge(item.status)}
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground mb-4">{item.description}</p>
                <div className="flex flex-wrap gap-2 mb-4">
                  {item.platforms.map((platform) => (
                    <span
                      key={platform}
                      className="px-2 py-1 bg-muted rounded-full text-xs"
                    >
                      {platform}
                    </span>
                  ))}
                </div>
                <div className="flex justify-between items-center">
                  <div className="text-sm text-muted-foreground">
                    {item.status === "scheduled" && item.scheduled_for ? (
                      <span>Scheduled for: {formatDate(item.scheduled_for)}</span>
                    ) : (
                      <span>Created: {formatDate(item.created_at)}</span>
                    )}
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      asChild
                    >
                      <Link href={`/content/${item.id}`}>View</Link>
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      asChild
                    >
                      <Link href={`/content/${item.id}/edit`}>Edit</Link>
                    </Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => handleDelete(item.id)}
                      disabled={isDeleting === item.id}
                    >
                      {isDeleting === item.id ? "Deleting..." : "Delete"}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
