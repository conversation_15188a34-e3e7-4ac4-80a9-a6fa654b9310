"use client";

import Image from "next/image";
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "../ui/Card";
import { Button } from "../ui/Button";

interface ContentPreviewProps {
  title: string;
  description: string;
  content: string;
  files: File[];
  platforms: string[];
  workflow: string;
}

export default function ContentPreview({
  title,
  description,
  content,
  files,
  platforms,
  workflow,
}: ContentPreviewProps) {
  const [selectedPlatform, setSelectedPlatform] = useState<string | null>(
    platforms.length > 0 ? platforms[0] : null
  );

  // Function to get platform name
  const getPlatformName = (platformId: string): string => {
    const platformMap: Record<string, string> = {
      youtube: "YouTube",
      instagram: "Instagram",
      linkedin: "LinkedIn",
      twitter: "Twitter",
      facebook: "Facebook",
      blog: "Blog",
    };
    return platformMap[platformId] || platformId;
  };

  // Function to get workflow name
  const getWorkflowName = (workflowId: string): string => {
    const workflowMap: Record<string, string> = {
      "social-media-blitz": "Social Media Blitz",
      "blog-post-generator": "Blog Post Generator",
      "video-content-suite": "Video Content Suite",
      "professional-linkedin": "Professional LinkedIn",
      custom: "Custom Workflow",
    };
    return workflowMap[workflowId] || workflowId;
  };

  // Function to render platform-specific preview
  const renderPlatformPreview = () => {
    if (!selectedPlatform) return null;

    switch (selectedPlatform) {
      case "instagram":
        return (
          <div className="border rounded-lg overflow-hidden">
            <div className="p-3 border-b flex items-center space-x-2">
              <div className="w-8 h-8 rounded-full bg-gray-200"></div>
              <div className="text-sm font-medium">Your Instagram Account</div>
            </div>
            {files.length > 0 && files[0].type.startsWith("image/") ? (
              <div className="aspect-square bg-gray-100 flex items-center justify-center">
                <p className="text-muted-foreground">Image Preview</p>
              </div>
            ) : (
              <div className="aspect-square bg-gray-100 flex items-center justify-center">
                <p className="text-muted-foreground">No image available</p>
              </div>
            )}
            <div className="p-3">
              <div className="flex items-center space-x-4 mb-2">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                  />
                </svg>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                  />
                </svg>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z"
                  />
                </svg>
              </div>
              <p className="font-medium text-sm">{title}</p>
              <p className="text-sm truncate">{description}</p>
            </div>
          </div>
        );
      case "linkedin":
        return (
          <div className="border rounded-lg overflow-hidden">
            <div className="p-3 border-b flex items-center space-x-2">
              <div className="w-8 h-8 rounded-full bg-gray-200"></div>
              <div>
                <div className="text-sm font-medium">Your Name</div>
                <div className="text-xs text-muted-foreground">Your Title</div>
              </div>
            </div>
            <div className="p-3">
              <p className="text-sm mb-3">{content.substring(0, 200)}...</p>
              {files.length > 0 && (
                <div className="rounded bg-gray-100 h-40 flex items-center justify-center mb-3">
                  <p className="text-muted-foreground">Media Preview</p>
                </div>
              )}
              <div className="flex items-center justify-between text-muted-foreground">
                <div className="flex items-center space-x-2">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5"
                    />
                  </svg>
                  <span>Like</span>
                </div>
                <div className="flex items-center space-x-2">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"
                    />
                  </svg>
                  <span>Comment</span>
                </div>
                <div className="flex items-center space-x-2">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z"
                    />
                  </svg>
                  <span>Share</span>
                </div>
              </div>
            </div>
          </div>
        );
      case "youtube":
        return (
          <div className="border rounded-lg overflow-hidden">
            <div className="aspect-video bg-gray-100 flex items-center justify-center">
              <div className="text-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-12 w-12 mx-auto text-muted-foreground"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z"
                  />
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                <p className="mt-2 text-muted-foreground">Video Preview</p>
              </div>
            </div>
            <div className="p-3">
              <h3 className="font-medium mb-1">{title}</h3>
              <p className="text-sm text-muted-foreground mb-2">
                Your Channel • 0 views • Just now
              </p>
              <p className="text-sm">{description}</p>
            </div>
          </div>
        );
      case "blog":
        return (
          <div className="border rounded-lg overflow-hidden">
            <div className="p-6">
              <h2 className="text-2xl font-bold mb-4">{title}</h2>
              <div className="flex items-center space-x-2 text-sm text-muted-foreground mb-6">
                <span>By Your Name</span>
                <span>•</span>
                <span>Just now</span>
              </div>
              {files.length > 0 && files[0].type.startsWith("image/") && (
                <div className="rounded bg-gray-100 h-48 flex items-center justify-center mb-6">
                  <p className="text-muted-foreground">Featured Image</p>
                </div>
              )}
              <div className="prose max-w-none">
                <p>{description}</p>
                <p>{content}</p>
              </div>
            </div>
          </div>
        );
      default:
        return (
          <div className="border rounded-lg p-6 text-center">
            <p className="text-muted-foreground">
              Preview not available for this platform yet.
            </p>
          </div>
        );
    }
  };

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <h3 className="font-medium">Content Summary</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="p-4 bg-muted/30 rounded-md">
            <p className="text-sm font-medium mb-1">Title</p>
            <p className="text-muted-foreground">{title}</p>
          </div>
          <div className="p-4 bg-muted/30 rounded-md">
            <p className="text-sm font-medium mb-1">Workflow</p>
            <p className="text-muted-foreground">{getWorkflowName(workflow)}</p>
          </div>
          <div className="p-4 bg-muted/30 rounded-md">
            <p className="text-sm font-medium mb-1">Platforms</p>
            <div className="flex flex-wrap gap-1">
              {platforms.map((platform) => (
                <span
                  key={platform}
                  className="px-2 py-1 bg-muted rounded-full text-xs"
                >
                  {getPlatformName(platform)}
                </span>
              ))}
            </div>
          </div>
          <div className="p-4 bg-muted/30 rounded-md">
            <p className="text-sm font-medium mb-1">Files</p>
            <p className="text-muted-foreground">
              {files.length > 0
                ? `${files.length} file(s) attached`
                : "No files attached"}
            </p>
          </div>
        </div>
      </div>

      {platforms.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="font-medium">Platform Preview</h3>
            <div className="flex space-x-2">
              {platforms.map((platform) => (
                <button
                  key={platform}
                  className={`px-3 py-1 rounded-md text-sm ${
                    selectedPlatform === platform
                      ? "bg-primary text-primary-foreground"
                      : "bg-muted hover:bg-muted/80"
                  }`}
                  onClick={() => setSelectedPlatform(platform)}
                >
                  {getPlatformName(platform)}
                </button>
              ))}
            </div>
          </div>
          <div className="max-w-md mx-auto">{renderPlatformPreview()}</div>
        </div>
      )}

      <div className="space-y-4">
        <h3 className="font-medium">Publishing Options</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Publish Now</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-4">
                Your content will be processed and published immediately to all selected platforms.
              </p>
              <Button variant="outline" fullWidth>
                Publish Now
              </Button>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Schedule</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-4">
                Choose a date and time to publish your content.
              </p>
              <div className="space-y-4">
                <input
                  type="datetime-local"
                  className="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                />
                <Button variant="outline" fullWidth>
                  Schedule
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
