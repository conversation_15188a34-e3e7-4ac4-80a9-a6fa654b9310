"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "../ui/Card";
import { Input } from "../ui/Input";
import { Button } from "../ui/Button";
import FileUpload from "./FileUpload";
import PlatformSelector from "./PlatformSelector";
import WorkflowSelector from "./WorkflowSelector";
import { useContent } from "../../context/ContentContext";
import { useToast } from "../../context/ToastContext";

type InputType = "text" | "document" | "image" | "spreadsheet" | "combined";

interface ContentEditFormProps {
  id: string;
}

export default function ContentEditForm({ id }: ContentEditFormProps) {
  const router = useRouter();
  const { contentItems, isLoading, error, fetchContentItems, updateContent } = useContent();
  const { showToast } = useToast();
  
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [inputType, setInputType] = useState<InputType>("text");
  const [textContent, setTextContent] = useState("");
  const [files, setFiles] = useState<File[]>([]);
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>([]);
  const [selectedWorkflow, setSelectedWorkflow] = useState("");
  const [status, setStatus] = useState<"draft" | "scheduled" | "published">("draft");
  const [scheduledFor, setScheduledFor] = useState<string | undefined>(undefined);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formLoaded, setFormLoaded] = useState(false);

  // Load content data
  useEffect(() => {
    fetchContentItems();
  }, [fetchContentItems]);

  useEffect(() => {
    if (contentItems.length > 0 && !formLoaded) {
      const contentItem = contentItems.find(item => item.id === id);
      if (contentItem) {
        setTitle(contentItem.title);
        setDescription(contentItem.description);
        setInputType(contentItem.content_type as InputType);
        setSelectedPlatforms(contentItem.platforms);
        setSelectedWorkflow(contentItem.workflow_id);
        setStatus(contentItem.status as "draft" | "scheduled" | "published");
        setScheduledFor(contentItem.scheduled_for);
        setFormLoaded(true);
      }
    }
  }, [contentItems, id, formLoaded]);

  const handleFileUpload = (uploadedFiles: File[]) => {
    setFiles([...files, ...uploadedFiles]);
  };

  const handleRemoveFile = (index: number) => {
    const newFiles = [...files];
    newFiles.splice(index, 1);
    setFiles(newFiles);
  };

  const handlePlatformChange = (platforms: string[]) => {
    setSelectedPlatforms(platforms);
  };

  const handleWorkflowChange = (workflow: string) => {
    setSelectedWorkflow(workflow);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    try {
      // Create content object
      const contentData = {
        title,
        description,
        content_type: inputType,
        status,
        platforms: selectedPlatforms,
        workflow_id: selectedWorkflow,
        scheduled_for: scheduledFor,
      };
      
      const { data, error } = await updateContent(id, contentData);
      
      if (error) {
        showToast("Error updating content", error, "error");
      } else {
        showToast(
          "Content updated successfully", 
          "Your content has been updated.", 
          "success"
        );
        
        // Redirect to content detail
        router.push(`/content/${id}`);
      }
    } catch (err: any) {
      showToast("Error updating content", err.message || "An unexpected error occurred", "error");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading || !formLoaded) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-between mb-8">
          <h1 className="text-3xl font-bold">Edit Content</h1>
        </div>
        <Card className="max-w-4xl mx-auto">
          <CardHeader>
            <CardTitle>Loading Content...</CardTitle>
            <CardDescription>Please wait while we load your content.</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-64 flex items-center justify-center">
              <div className="w-12 h-12 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-between mb-8">
          <h1 className="text-3xl font-bold">Edit Content</h1>
        </div>
        <Card className="max-w-4xl mx-auto">
          <CardHeader>
            <CardTitle>Error Loading Content</CardTitle>
            <CardDescription>There was an error loading the content.</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="p-6 border border-error/50 bg-error/10 rounded-lg text-error">
              <p>{error}</p>
              <Button 
                onClick={() => fetchContentItems()} 
                variant="outline" 
                className="mt-4"
              >
                Try Again
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center">
          <Button 
            variant="outline" 
            size="sm" 
            className="mr-4"
            onClick={() => router.back()}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 mr-2"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M10 19l-7-7m0 0l7-7m-7 7h18"
              />
            </svg>
            Back
          </Button>
          <h1 className="text-3xl font-bold">Edit Content</h1>
        </div>
      </div>

      <Card className="max-w-4xl mx-auto">
        <form onSubmit={handleSubmit}>
          <CardHeader>
            <CardTitle>Edit Content</CardTitle>
            <CardDescription>
              Update your content information
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Basic Information</h3>
              <Input
                id="title"
                label="Content Title"
                placeholder="Enter a title for your content"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                required
              />
              <div className="space-y-2">
                <label htmlFor="description" className="block text-sm font-medium">
                  Description
                </label>
                <textarea
                  id="description"
                  className="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  rows={3}
                  placeholder="Enter a brief description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                />
              </div>
            </div>

            {/* Content Type */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Content Type</h3>
              <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
                {["text", "document", "image", "spreadsheet", "combined"].map((type) => (
                  <button
                    key={type}
                    type="button"
                    className={`px-3 py-2 rounded-md text-sm ${
                      inputType === type
                        ? "bg-primary text-primary-foreground"
                        : "bg-muted hover:bg-muted/80"
                    }`}
                    onClick={() => setInputType(type as InputType)}
                  >
                    {type.charAt(0).toUpperCase() + type.slice(1)}
                  </button>
                ))}
              </div>
            </div>

            {/* Content */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Content</h3>
              {inputType === "text" ? (
                <div className="space-y-2">
                  <label htmlFor="textContent" className="block text-sm font-medium">
                    Text Content
                  </label>
                  <textarea
                    id="textContent"
                    className="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    rows={10}
                    placeholder="Enter your content here..."
                    value={textContent}
                    onChange={(e) => setTextContent(e.target.value)}
                  />
                </div>
              ) : (
                <FileUpload
                  onFileUpload={handleFileUpload}
                  onRemoveFile={handleRemoveFile}
                  files={files}
                  acceptedFileTypes={
                    inputType === "document"
                      ? ".pdf,.doc,.docx,.txt"
                      : inputType === "image"
                      ? ".jpg,.jpeg,.png,.gif"
                      : inputType === "spreadsheet"
                      ? ".xls,.xlsx,.csv"
                      : undefined
                  }
                  maxFileSize={50 * 1024 * 1024} // 50MB
                />
              )}
            </div>

            {/* Platforms */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Target Platforms</h3>
              <PlatformSelector
                selectedPlatforms={selectedPlatforms}
                onChange={handlePlatformChange}
              />
            </div>

            {/* Workflow */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Workflow</h3>
              <WorkflowSelector
                selectedWorkflow={selectedWorkflow}
                onChange={handleWorkflowChange}
              />
            </div>

            {/* Status */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Status</h3>
              <div className="grid grid-cols-3 gap-2">
                <button
                  type="button"
                  className={`px-3 py-2 rounded-md text-sm ${
                    status === "draft"
                      ? "bg-primary text-primary-foreground"
                      : "bg-muted hover:bg-muted/80"
                  }`}
                  onClick={() => setStatus("draft")}
                >
                  Draft
                </button>
                <button
                  type="button"
                  className={`px-3 py-2 rounded-md text-sm ${
                    status === "scheduled"
                      ? "bg-primary text-primary-foreground"
                      : "bg-muted hover:bg-muted/80"
                  }`}
                  onClick={() => setStatus("scheduled")}
                >
                  Scheduled
                </button>
                <button
                  type="button"
                  className={`px-3 py-2 rounded-md text-sm ${
                    status === "published"
                      ? "bg-primary text-primary-foreground"
                      : "bg-muted hover:bg-muted/80"
                  }`}
                  onClick={() => setStatus("published")}
                >
                  Published
                </button>
              </div>
            </div>

            {/* Schedule */}
            {status === "scheduled" && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Schedule</h3>
                <div className="space-y-2">
                  <label htmlFor="scheduledFor" className="block text-sm font-medium">
                    Scheduled Date and Time
                  </label>
                  <input
                    id="scheduledFor"
                    type="datetime-local"
                    className="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    value={scheduledFor ? new Date(scheduledFor).toISOString().slice(0, 16) : ""}
                    onChange={(e) => setScheduledFor(e.target.value ? new Date(e.target.value).toISOString() : undefined)}
                  />
                </div>
              </div>
            )}
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => router.back()}
            >
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={isSubmitting}
            >
              {isSubmitting ? "Saving..." : "Save Changes"}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
}
