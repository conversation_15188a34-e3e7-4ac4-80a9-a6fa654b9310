"use client";

import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, Responsive<PERSON><PERSON><PERSON>, <PERSON>, Tooltip } from "recharts";

interface ContentTypeDistributionProps {
  data: Record<string, number>;
}

export default function ContentTypeDistribution({ data }: ContentTypeDistributionProps) {
  // Transform data for chart
  const chartData = Object.entries(data).map(([name, value]) => ({
    name: name.charAt(0).toUpperCase() + name.slice(1),
    value,
  }));

  // Colors for different content types
  const COLORS = {
    text: "#3B82F6",
    image: "#10B981",
    video: "#F59E0B",
    document: "#8B5CF6",
    spreadsheet: "#EC4899",
    combined: "#6366F1",
    default: "#6B7280",
  };

  // Get color for content type
  const getContentTypeColor = (type: string) => {
    const key = type.toLowerCase() as keyof typeof COLORS;
    return COLORS[key] || COLORS.default;
  };

  // Custom tooltip
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-2 border rounded-md shadow-sm">
          <p className="font-medium">{`${payload[0].name}: ${payload[0].value}`}</p>
          <p className="text-sm text-muted-foreground">{`${((payload[0].value / chartData.reduce((sum, item) => sum + item.value, 0)) * 100).toFixed(1)}%`}</p>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="h-64">
      <ResponsiveContainer width="100%" height="100%">
        <PieChart>
          <Pie
            data={chartData}
            cx="50%"
            cy="50%"
            labelLine={false}
            outerRadius={80}
            fill="#8884d8"
            dataKey="value"
            label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
          >
            {chartData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={getContentTypeColor(entry.name)} />
            ))}
          </Pie>
          <Tooltip content={<CustomTooltip />} />
          <Legend />
        </PieChart>
      </ResponsiveContainer>
    </div>
  );
}
