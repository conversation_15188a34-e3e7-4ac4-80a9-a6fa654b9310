"use client";

import { Card, CardContent } from "../ui/Card";
import { Skeleton } from "../ui/Skeleton";
import { AnalyticsData } from "../../context/AnalyticsContext";

interface OverviewStatsProps {
  isLoading: boolean;
  analyticsData: AnalyticsData | null;
}

export default function OverviewStats({ isLoading, analyticsData }: OverviewStatsProps) {
  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + "M";
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + "K";
    } else {
      return num.toString();
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {/* Content Count */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Total Content</p>
              {isLoading ? (
                <Skeleton className="h-8 w-16 mt-1" />
              ) : (
                <p className="text-3xl font-bold">{analyticsData?.contentCount || 0}</p>
              )}
            </div>
            <div className="rounded-full bg-primary/10 p-3 text-primary">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                />
              </svg>
            </div>
          </div>
          <div className="mt-4 flex items-center justify-between">
            <div className="grid grid-cols-3 gap-2 text-xs">
              <div className="flex flex-col">
                <span className="text-muted-foreground">Published</span>
                {isLoading ? (
                  <Skeleton className="h-4 w-8 mt-1" />
                ) : (
                  <span className="font-medium">{analyticsData?.publishedCount || 0}</span>
                )}
              </div>
              <div className="flex flex-col">
                <span className="text-muted-foreground">Scheduled</span>
                {isLoading ? (
                  <Skeleton className="h-4 w-8 mt-1" />
                ) : (
                  <span className="font-medium">{analyticsData?.scheduledCount || 0}</span>
                )}
              </div>
              <div className="flex flex-col">
                <span className="text-muted-foreground">Drafts</span>
                {isLoading ? (
                  <Skeleton className="h-4 w-8 mt-1" />
                ) : (
                  <span className="font-medium">{analyticsData?.draftCount || 0}</span>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Views */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Total Views</p>
              {isLoading ? (
                <Skeleton className="h-8 w-16 mt-1" />
              ) : (
                <p className="text-3xl font-bold">{formatNumber(analyticsData?.engagementMetrics.views || 0)}</p>
              )}
            </div>
            <div className="rounded-full bg-blue-100 p-3 text-blue-600">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                />
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                />
              </svg>
            </div>
          </div>
          <div className="mt-4">
            <div className="h-2 bg-blue-100 rounded-full">
              <div className="h-2 bg-blue-500 rounded-full" style={{ width: "70%" }}></div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Engagement */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Engagement</p>
              {isLoading ? (
                <Skeleton className="h-8 w-16 mt-1" />
              ) : (
                <p className="text-3xl font-bold">
                  {formatNumber(
                    (analyticsData?.engagementMetrics.likes || 0) +
                    (analyticsData?.engagementMetrics.comments || 0) +
                    (analyticsData?.engagementMetrics.shares || 0)
                  )}
                </p>
              )}
            </div>
            <div className="rounded-full bg-green-100 p-3 text-green-600">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5"
                />
              </svg>
            </div>
          </div>
          <div className="mt-4 grid grid-cols-3 gap-2 text-xs">
            <div className="flex flex-col">
              <span className="text-muted-foreground">Likes</span>
              {isLoading ? (
                <Skeleton className="h-4 w-8 mt-1" />
              ) : (
                <span className="font-medium">{formatNumber(analyticsData?.engagementMetrics.likes || 0)}</span>
              )}
            </div>
            <div className="flex flex-col">
              <span className="text-muted-foreground">Comments</span>
              {isLoading ? (
                <Skeleton className="h-4 w-8 mt-1" />
              ) : (
                <span className="font-medium">{formatNumber(analyticsData?.engagementMetrics.comments || 0)}</span>
              )}
            </div>
            <div className="flex flex-col">
              <span className="text-muted-foreground">Shares</span>
              {isLoading ? (
                <Skeleton className="h-4 w-8 mt-1" />
              ) : (
                <span className="font-medium">{formatNumber(analyticsData?.engagementMetrics.shares || 0)}</span>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Engagement Rate */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Engagement Rate</p>
              {isLoading ? (
                <Skeleton className="h-8 w-16 mt-1" />
              ) : (
                <p className="text-3xl font-bold">
                  {analyticsData?.engagementMetrics.views
                    ? (
                        ((analyticsData.engagementMetrics.likes +
                          analyticsData.engagementMetrics.comments +
                          analyticsData.engagementMetrics.shares) /
                          analyticsData.engagementMetrics.views) *
                        100
                      ).toFixed(1)
                    : "0"}%
                </p>
              )}
            </div>
            <div className="rounded-full bg-purple-100 p-3 text-purple-600">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                />
              </svg>
            </div>
          </div>
          <div className="mt-4">
            <div className="h-2 bg-purple-100 rounded-full">
              <div
                className="h-2 bg-purple-500 rounded-full"
                style={{
                  width: isLoading
                    ? "0%"
                    : analyticsData?.engagementMetrics.views
                    ? `${Math.min(
                        ((analyticsData.engagementMetrics.likes +
                          analyticsData.engagementMetrics.comments +
                          analyticsData.engagementMetrics.shares) /
                          analyticsData.engagementMetrics.views) *
                          100 *
                          5,
                        100
                      )}%`
                    : "0%",
                }}
              ></div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
