"use client";

import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, Responsive<PERSON><PERSON><PERSON>, <PERSON>, Tooltip } from "recharts";

interface PlatformDistributionProps {
  data: Record<string, number>;
}

export default function PlatformDistribution({ data }: PlatformDistributionProps) {
  // Transform data for chart
  const chartData = Object.entries(data).map(([name, value]) => ({
    name: name.charAt(0).toUpperCase() + name.slice(1),
    value,
  }));

  // Colors for different platforms
  const COLORS = {
    instagram: "#E1306C",
    twitter: "#1DA1F2",
    facebook: "#4267B2",
    linkedin: "#0077B5",
    youtube: "#FF0000",
    blog: "#2ECC71",
    default: "#6B7280",
  };

  // Get color for platform
  const getPlatformColor = (platform: string) => {
    const key = platform.toLowerCase() as keyof typeof COLORS;
    return COLORS[key] || COLORS.default;
  };

  // Custom tooltip
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-2 border rounded-md shadow-sm">
          <p className="font-medium">{`${payload[0].name}: ${payload[0].value}`}</p>
          <p className="text-sm text-muted-foreground">{`${((payload[0].value / chartData.reduce((sum, item) => sum + item.value, 0)) * 100).toFixed(1)}%`}</p>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="h-64">
      <ResponsiveContainer width="100%" height="100%">
        <PieChart>
          <Pie
            data={chartData}
            cx="50%"
            cy="50%"
            labelLine={false}
            outerRadius={80}
            fill="#8884d8"
            dataKey="value"
            label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
          >
            {chartData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={getPlatformColor(entry.name)} />
            ))}
          </Pie>
          <Tooltip content={<CustomTooltip />} />
          <Legend />
        </PieChart>
      </ResponsiveContainer>
    </div>
  );
}
