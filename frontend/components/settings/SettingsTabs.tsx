"use client";

import { useState } from "react";
import { Card, CardContent } from "../ui/Card";
import ProfileSettings from "./ProfileSettings";
import NotificationSettings from "./NotificationSettings";
import IntegrationSettings from "./IntegrationSettings";
import AccountSettings from "./AccountSettings";

export default function SettingsTabs() {
  const [activeTab, setActiveTab] = useState("profile");

  const tabs = [
    { id: "profile", label: "Profile" },
    { id: "notifications", label: "Notifications" },
    { id: "integrations", label: "Integrations" },
    { id: "account", label: "Account" },
  ];

  return (
    <div className="flex flex-col md:flex-row gap-8">
      {/* Tabs */}
      <div className="w-full md:w-64 shrink-0">
        <div className="space-y-1">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              className={`w-full text-left px-4 py-2 rounded-md transition-colors ${
                activeTab === tab.id
                  ? "bg-primary text-primary-foreground"
                  : "hover:bg-muted"
              }`}
              onClick={() => setActiveTab(tab.id)}
            >
              {tab.label}
            </button>
          ))}
        </div>
      </div>

      {/* Content */}
      <Card className="flex-1">
        <CardContent className="p-6">
          {activeTab === "profile" && <ProfileSettings />}
          {activeTab === "notifications" && <NotificationSettings />}
          {activeTab === "integrations" && <IntegrationSettings />}
          {activeTab === "account" && <AccountSettings />}
        </CardContent>
      </Card>
    </div>
  );
}
