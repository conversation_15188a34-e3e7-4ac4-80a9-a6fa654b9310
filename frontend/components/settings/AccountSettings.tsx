"use client";

import { useState } from "react";
import { Input } from "../ui/Input";
import { Button } from "../ui/Button";
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "../ui/Card";
import { useAuth } from "../../context/AuthContext";
import { useToast } from "../../context/ToastContext";

export default function AccountSettings() {
  const { user, signOut } = useAuth();
  const { showToast } = useToast();
  
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [isChangingPassword, setIsChangingPassword] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [deleteConfirmation, setDeleteConfirmation] = useState("");

  const handleChangePassword = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (newPassword !== confirmPassword) {
      showToast(
        "Passwords don't match",
        "New password and confirmation password must match.",
        "error"
      );
      return;
    }
    
    setIsChangingPassword(true);
    
    try {
      // In a real app, we would update the password
      // const { error } = await supabase.auth.updateUser({
      //   password: newPassword,
      // });
      
      // if (error) throw error;
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      showToast(
        "Password updated",
        "Your password has been updated successfully.",
        "success"
      );
      
      // Reset form
      setCurrentPassword("");
      setNewPassword("");
      setConfirmPassword("");
    } catch (err: any) {
      showToast(
        "Error updating password",
        err.message || "An error occurred while updating your password.",
        "error"
      );
    } finally {
      setIsChangingPassword(false);
    }
  };

  const handleDeleteAccount = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (deleteConfirmation !== "DELETE") {
      showToast(
        "Confirmation required",
        "Please type DELETE to confirm account deletion.",
        "error"
      );
      return;
    }
    
    setIsDeleting(true);
    
    try {
      // In a real app, we would delete the user account
      // const { error } = await supabase.rpc('delete_user_account');
      
      // if (error) throw error;
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      showToast(
        "Account deleted",
        "Your account has been deleted successfully.",
        "success"
      );
      
      // Sign out the user
      await signOut();
    } catch (err: any) {
      showToast(
        "Error deleting account",
        err.message || "An error occurred while deleting your account.",
        "error"
      );
      setIsDeleting(false);
    }
  };

  return (
    <div>
      <h2 className="text-2xl font-bold mb-6">Account Settings</h2>
      
      <div className="space-y-8">
        {/* Account Information */}
        <div>
          <h3 className="text-lg font-medium mb-4">Account Information</h3>
          <div className="space-y-4">
            <Input
              id="email"
              label="Email Address"
              value={user?.email || ""}
              disabled
            />
            
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Account Type</p>
                <p className="text-sm text-muted-foreground">Free Plan</p>
              </div>
              <Button variant="outline" size="sm" asChild>
                <a href="/pricing">Upgrade</a>
              </Button>
            </div>
            
            <div>
              <p className="font-medium">Account Created</p>
              <p className="text-sm text-muted-foreground">
                {user?.created_at
                  ? new Date(user.created_at).toLocaleDateString("en-US", {
                      year: "numeric",
                      month: "long",
                      day: "numeric",
                    })
                  : "Unknown"}
              </p>
            </div>
          </div>
        </div>
        
        {/* Change Password */}
        <div>
          <h3 className="text-lg font-medium mb-4">Change Password</h3>
          <form onSubmit={handleChangePassword} className="space-y-4">
            <Input
              id="currentPassword"
              label="Current Password"
              type="password"
              placeholder="Enter your current password"
              value={currentPassword}
              onChange={(e) => setCurrentPassword(e.target.value)}
              required
            />
            
            <Input
              id="newPassword"
              label="New Password"
              type="password"
              placeholder="Enter your new password"
              value={newPassword}
              onChange={(e) => setNewPassword(e.target.value)}
              required
            />
            
            <Input
              id="confirmPassword"
              label="Confirm New Password"
              type="password"
              placeholder="Confirm your new password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              required
            />
            
            <div className="flex justify-end">
              <Button type="submit" disabled={isChangingPassword}>
                {isChangingPassword ? "Updating..." : "Update Password"}
              </Button>
            </div>
          </form>
        </div>
        
        {/* Delete Account */}
        <Card className="border-error/50">
          <CardHeader>
            <CardTitle className="text-error">Delete Account</CardTitle>
            <CardDescription>
              This action is permanent and cannot be undone. All your data will be permanently deleted.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleDeleteAccount} className="space-y-4">
              <div className="space-y-2">
                <label htmlFor="deleteConfirmation" className="block text-sm font-medium">
                  Type DELETE to confirm
                </label>
                <input
                  id="deleteConfirmation"
                  className="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-error focus:border-transparent"
                  placeholder="DELETE"
                  value={deleteConfirmation}
                  onChange={(e) => setDeleteConfirmation(e.target.value)}
                  required
                />
              </div>
              
              <div className="flex justify-end">
                <Button type="submit" variant="destructive" disabled={isDeleting}>
                  {isDeleting ? "Deleting..." : "Delete Account"}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
