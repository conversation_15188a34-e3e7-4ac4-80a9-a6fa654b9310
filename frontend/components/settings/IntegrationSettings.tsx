"use client";

import { useState } from "react";
import { Button } from "../ui/Button";
import { useToast } from "../../context/ToastContext";

interface Integration {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  connected: boolean;
  accountName?: string;
}

export default function IntegrationSettings() {
  const { showToast } = useToast();
  
  const [integrations, setIntegrations] = useState<Integration[]>([
    {
      id: "instagram",
      name: "Instagram",
      description: "Connect your Instagram account to publish content directly.",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-6 w-6 text-pink-600"
          viewBox="0 0 24 24"
          fill="currentColor"
        >
          <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z" />
        </svg>
      ),
      connected: false,
    },
    {
      id: "youtube",
      name: "YouTube",
      description: "Connect your YouTube channel to publish videos directly.",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-6 w-6 text-red-600"
          viewBox="0 0 24 24"
          fill="currentColor"
        >
          <path d="M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z" />
        </svg>
      ),
      connected: true,
      accountName: "ContentForge Channel",
    },
    {
      id: "linkedin",
      name: "LinkedIn",
      description: "Connect your LinkedIn profile to share content with your network.",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-6 w-6 text-blue-600"
          viewBox="0 0 24 24"
          fill="currentColor"
        >
          <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z" />
        </svg>
      ),
      connected: false,
    },
    {
      id: "twitter",
      name: "Twitter",
      description: "Connect your Twitter account to post tweets directly.",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-6 w-6 text-blue-400"
          viewBox="0 0 24 24"
          fill="currentColor"
        >
          <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z" />
        </svg>
      ),
      connected: true,
      accountName: "@contentforge",
    },
    {
      id: "facebook",
      name: "Facebook",
      description: "Connect your Facebook page to publish posts directly.",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-6 w-6 text-blue-600"
          viewBox="0 0 24 24"
          fill="currentColor"
        >
          <path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z" />
        </svg>
      ),
      connected: false,
    },
    {
      id: "wordpress",
      name: "WordPress",
      description: "Connect your WordPress blog to publish articles directly.",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-6 w-6 text-blue-500"
          viewBox="0 0 24 24"
          fill="currentColor"
        >
          <path d="M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm-1.834 9.686c-.145.652-.258 1.336-.258 2.064 0 .613.094 1.297.094 1.914 0 .617-.094 1.281-.094 1.898 0 .184.016.363.016.547l1.014-2.781 1.125-3.109c.371-.996.328-1.432-.086-1.457-.414-.023-.867.219-1.172.414-.305.199-.539.363-.539.363-.145.094-.25.164-.25.164.074-.398.25-.75.25-.75s.539-.727 1.383-1.09c.84-.367 1.57-.305 1.57-.305.398.047.883.258 1.195.609.312.348.266.996.266.996l-1.445 4.141-1.719 4.754c.348-.094.652-.164.652-.164.305-.074.27-.5-.031-.484 0 0-.969.078-.969.078l-2.953-.078s-.793-.02-.793.484c0 .5.363.441.363.441.184.031.398.051.398.051l.582-1.578.969-2.617zm1.834-5.686c-5.514 0-10 4.486-10 10s4.486 10 10 10 10-4.486 10-10-4.486-10-10-10zm0 19.75c-5.379 0-9.75-4.371-9.75-9.75s4.371-9.75 9.75-9.75 9.75 4.371 9.75 9.75-4.371 9.75-9.75 9.75z" />
        </svg>
      ),
      connected: true,
      accountName: "ContentForge Blog",
    },
  ]);
  
  const [isConnecting, setIsConnecting] = useState<string | null>(null);
  const [isDisconnecting, setIsDisconnecting] = useState<string | null>(null);

  const handleConnect = async (id: string) => {
    setIsConnecting(id);
    
    try {
      // In a real app, we would initiate OAuth flow
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Update integration status
      setIntegrations(integrations.map(integration => 
        integration.id === id 
          ? { ...integration, connected: true, accountName: `Test Account (${integration.name})` } 
          : integration
      ));
      
      showToast(
        "Integration connected",
        `Your ${integrations.find(i => i.id === id)?.name} account has been connected successfully.`,
        "success"
      );
    } catch (err: any) {
      showToast(
        "Error connecting integration",
        err.message || "An error occurred while connecting the integration.",
        "error"
      );
    } finally {
      setIsConnecting(null);
    }
  };

  const handleDisconnect = async (id: string) => {
    if (confirm("Are you sure you want to disconnect this integration? You will need to reconnect it to use it again.")) {
      setIsDisconnecting(id);
      
      try {
        // In a real app, we would revoke OAuth tokens
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Update integration status
        setIntegrations(integrations.map(integration => 
          integration.id === id 
            ? { ...integration, connected: false, accountName: undefined } 
            : integration
        ));
        
        showToast(
          "Integration disconnected",
          `Your ${integrations.find(i => i.id === id)?.name} account has been disconnected.`,
          "success"
        );
      } catch (err: any) {
        showToast(
          "Error disconnecting integration",
          err.message || "An error occurred while disconnecting the integration.",
          "error"
        );
      } finally {
        setIsDisconnecting(null);
      }
    }
  };

  return (
    <div>
      <h2 className="text-2xl font-bold mb-6">Integrations</h2>
      
      <div className="space-y-6">
        {integrations.map((integration) => (
          <div key={integration.id} className="flex items-start justify-between p-4 border rounded-lg">
            <div className="flex items-start space-x-4">
              <div className="rounded-full bg-muted/50 p-2">
                {integration.icon}
              </div>
              <div>
                <h3 className="font-medium">{integration.name}</h3>
                <p className="text-sm text-muted-foreground">{integration.description}</p>
                {integration.connected && integration.accountName && (
                  <p className="text-sm mt-1">
                    Connected as <span className="font-medium">{integration.accountName}</span>
                  </p>
                )}
              </div>
            </div>
            <div>
              {integration.connected ? (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleDisconnect(integration.id)}
                  disabled={isDisconnecting === integration.id}
                >
                  {isDisconnecting === integration.id ? "Disconnecting..." : "Disconnect"}
                </Button>
              ) : (
                <Button
                  size="sm"
                  onClick={() => handleConnect(integration.id)}
                  disabled={isConnecting === integration.id}
                >
                  {isConnecting === integration.id ? "Connecting..." : "Connect"}
                </Button>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
