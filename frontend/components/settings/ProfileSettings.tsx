"use client";

import { useState } from "react";
import { Input } from "../ui/Input";
import { Button } from "../ui/Button";
import { useAuth } from "../../context/AuthContext";
import { useToast } from "../../context/ToastContext";

export default function ProfileSettings() {
  const { user } = useAuth();
  const { showToast } = useToast();
  
  const [fullName, setFullName] = useState(user?.user_metadata?.full_name || "");
  const [company, setCompany] = useState(user?.user_metadata?.company || "");
  const [jobTitle, setJobTitle] = useState(user?.user_metadata?.job_title || "");
  const [bio, setBio] = useState(user?.user_metadata?.bio || "");
  const [website, setWebsite] = useState(user?.user_metadata?.website || "");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    try {
      // In a real app, we would update the user metadata
      // const { error } = await supabase.auth.updateUser({
      //   data: {
      //     full_name: fullName,
      //     company,
      //     job_title: jobTitle,
      //     bio,
      //     website,
      //   },
      // });
      
      // if (error) throw error;
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      showToast(
        "Profile updated",
        "Your profile information has been updated successfully.",
        "success"
      );
    } catch (err: any) {
      showToast(
        "Error updating profile",
        err.message || "An error occurred while updating your profile.",
        "error"
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div>
      <h2 className="text-2xl font-bold mb-6">Profile Settings</h2>
      
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="flex items-center space-x-4 mb-8">
          <div className="w-20 h-20 rounded-full bg-primary/20 flex items-center justify-center text-primary text-2xl font-medium">
            {user?.email?.charAt(0).toUpperCase()}
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Profile Picture</p>
            <div className="flex space-x-2 mt-2">
              <Button type="button" variant="outline" size="sm">
                Upload
              </Button>
              <Button type="button" variant="outline" size="sm">
                Remove
              </Button>
            </div>
          </div>
        </div>
        
        <Input
          id="email"
          label="Email Address"
          value={user?.email || ""}
          disabled
          required
        />
        
        <Input
          id="fullName"
          label="Full Name"
          placeholder="Enter your full name"
          value={fullName}
          onChange={(e) => setFullName(e.target.value)}
        />
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input
            id="company"
            label="Company"
            placeholder="Enter your company name"
            value={company}
            onChange={(e) => setCompany(e.target.value)}
          />
          
          <Input
            id="jobTitle"
            label="Job Title"
            placeholder="Enter your job title"
            value={jobTitle}
            onChange={(e) => setJobTitle(e.target.value)}
          />
        </div>
        
        <div className="space-y-2">
          <label htmlFor="bio" className="block text-sm font-medium">
            Bio
          </label>
          <textarea
            id="bio"
            className="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            rows={4}
            placeholder="Tell us about yourself"
            value={bio}
            onChange={(e) => setBio(e.target.value)}
          />
        </div>
        
        <Input
          id="website"
          label="Website"
          placeholder="https://example.com"
          value={website}
          onChange={(e) => setWebsite(e.target.value)}
        />
        
        <div className="flex justify-end">
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? "Saving..." : "Save Changes"}
          </Button>
        </div>
      </form>
    </div>
  );
}
