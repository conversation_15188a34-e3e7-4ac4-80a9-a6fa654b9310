"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { Button } from "../ui/Button";
import { <PERSON>, CardHeader, <PERSON><PERSON><PERSON><PERSON>, CardContent, CardFooter } from "../ui/Card";
import { Skeleton } from "../ui/Skeleton";
import { useWorkflow } from "../../context/WorkflowContext";
import { useToast } from "../../context/ToastContext";
import { formatDate } from "../../lib/utils";

interface WorkflowDetailProps {
  id: string;
}

export default function WorkflowDetail({ id }: WorkflowDetailProps) {
  const router = useRouter();
  const { workflows, isLoading, error, fetchWorkflows, getWorkflow, deleteWorkflow } = useWorkflow();
  const { showToast } = useToast();
  const [isDeleting, setIsDeleting] = useState(false);
  const [workflow, setWorkflow] = useState<any>(null);

  useEffect(() => {
    fetchWorkflows();
  }, [fetchWorkflows]);

  useEffect(() => {
    if (workflows.length > 0) {
      const workflowData = getWorkflow(id);
      if (workflowData) {
        setWorkflow(workflowData);
      }
    }
  }, [workflows, id, getWorkflow]);

  const handleDelete = async () => {
    if (confirm("Are you sure you want to delete this workflow? This action cannot be undone.")) {
      setIsDeleting(true);
      const { error } = await deleteWorkflow(id);
      
      if (error) {
        showToast("Error deleting workflow", error, "error");
      } else {
        showToast("Workflow deleted", "The workflow has been successfully deleted.", "success");
        router.push("/workflows");
      }
      
      setIsDeleting(false);
    }
  };

  // Get platform badge
  const getPlatformBadge = (platform: string) => {
    const baseClasses = "px-2 py-1 rounded-full text-xs font-medium";
    
    switch (platform.toLowerCase()) {
      case "instagram":
        return <span className={`${baseClasses} bg-pink-100 text-pink-800`}>{platform}</span>;
      case "youtube":
        return <span className={`${baseClasses} bg-red-100 text-red-800`}>{platform}</span>;
      case "linkedin":
        return <span className={`${baseClasses} bg-blue-100 text-blue-800`}>{platform}</span>;
      case "twitter":
        return <span className={`${baseClasses} bg-blue-100 text-blue-800`}>{platform}</span>;
      case "facebook":
        return <span className={`${baseClasses} bg-blue-100 text-blue-800`}>{platform}</span>;
      case "blog":
        return <span className={`${baseClasses} bg-green-100 text-green-800`}>{platform}</span>;
      default:
        return <span className={`${baseClasses} bg-gray-100 text-gray-800`}>{platform}</span>;
    }
  };

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="p-6 border border-error/50 bg-error/10 rounded-lg text-error">
          <h3 className="font-medium mb-2">Error loading workflow</h3>
          <p>{error}</p>
          <Button 
            onClick={() => fetchWorkflows()} 
            variant="outline" 
            className="mt-4"
          >
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  if (isLoading || !workflow) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-between mb-8">
          <Skeleton className="h-8 w-64" />
          <Skeleton className="h-10 w-24" />
        </div>
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <Skeleton className="h-8 w-1/3" />
              <Skeleton className="h-6 w-20" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
              <div className="flex flex-wrap gap-2 my-4">
                <Skeleton className="h-6 w-20 rounded-full" />
                <Skeleton className="h-6 w-20 rounded-full" />
                <Skeleton className="h-6 w-20 rounded-full" />
              </div>
              <div className="space-y-4 mt-6">
                <Skeleton className="h-20 w-full" />
                <Skeleton className="h-20 w-full" />
                <Skeleton className="h-20 w-full" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center">
          <Button 
            variant="outline" 
            size="sm" 
            className="mr-4"
            onClick={() => router.back()}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 mr-2"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M10 19l-7-7m0 0l7-7m-7 7h18"
              />
            </svg>
            Back
          </Button>
          <h1 className="text-3xl font-bold">Workflow Details</h1>
        </div>
        <div className="flex space-x-2">
          <Button 
            variant="outline" 
            asChild
          >
            <Link href={`/workflows/${id}/edit`}>Edit Workflow</Link>
          </Button>
          <Button 
            variant="destructive" 
            onClick={handleDelete}
            disabled={isDeleting}
          >
            {isDeleting ? "Deleting..." : "Delete"}
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader className="pb-2">
          <div className="flex justify-between items-center">
            <CardTitle className="text-2xl">{workflow.name}</CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-2">Description</h3>
              <p className="text-muted-foreground">{workflow.description}</p>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2">Platforms</h3>
              <div className="flex flex-wrap gap-2">
                {workflow.platforms.map((platform: string) => (
                  <div key={platform}>
                    {getPlatformBadge(platform)}
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2">Workflow Steps</h3>
              {workflow.steps.length === 0 ? (
                <p className="text-muted-foreground">No steps defined for this workflow.</p>
              ) : (
                <div className="space-y-4">
                  {workflow.steps
                    .sort((a: any, b: any) => a.position - b.position)
                    .map((step: any, index: number) => (
                      <div key={step.id} className="border rounded-lg p-4 bg-card">
                        <div className="flex items-start space-x-4">
                          <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary/10 text-primary font-medium">
                            {index + 1}
                          </div>
                          <div>
                            <h4 className="font-medium">{step.name}</h4>
                            <p className="text-sm text-muted-foreground">{step.description}</p>
                            <div className="mt-2">
                              <span className="px-2 py-1 bg-muted rounded-full text-xs">
                                {step.type}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                </div>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t">
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">Created</h3>
                <p>{formatDate(workflow.created_at)}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">Last Updated</h3>
                <p>{formatDate(workflow.updated_at)}</p>
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between border-t pt-6">
          <Button 
            variant="outline" 
            onClick={() => router.push("/workflows")}
          >
            Back to Workflows
          </Button>
          <Button asChild>
            <Link href={`/content/new?workflow=${id}`}>
              Create Content with this Workflow
            </Link>
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
