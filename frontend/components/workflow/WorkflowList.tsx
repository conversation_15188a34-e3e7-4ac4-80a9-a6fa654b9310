"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { Button } from "../ui/Button";
import { <PERSON>, CardH<PERSON>er, CardTitle, CardContent } from "../ui/Card";
import { Skeleton } from "../ui/Skeleton";
import { useWorkflow } from "../../context/WorkflowContext";
import { useToast } from "../../context/ToastContext";
import { formatDate } from "../../lib/utils";

export default function WorkflowList() {
  const { workflows, isLoading, error, fetchWorkflows, deleteWorkflow } = useWorkflow();
  const { showToast } = useToast();
  const [filter, setFilter] = useState<string>("all");
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [isDeleting, setIsDeleting] = useState<string | null>(null);

  useEffect(() => {
    fetchWorkflows();
  }, [fetchWorkflows]);

  const handleDelete = async (id: string) => {
    if (confirm("Are you sure you want to delete this workflow? This action cannot be undone.")) {
      setIsDeleting(id);
      const { error } = await deleteWorkflow(id);
      
      if (error) {
        showToast("Error deleting workflow", error, "error");
      } else {
        showToast("Workflow deleted", "The workflow has been successfully deleted.", "success");
      }
      
      setIsDeleting(null);
    }
  };

  // Filter and search workflows
  const filteredWorkflows = workflows.filter(workflow => {
    const matchesSearch = searchTerm === "" || 
      workflow.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      workflow.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    if (filter === "all") return matchesSearch;
    
    return matchesSearch && workflow.platforms.includes(filter);
  });

  if (error) {
    return (
      <div className="p-6 border border-error/50 bg-error/10 rounded-lg text-error">
        <h3 className="font-medium mb-2">Error loading workflows</h3>
        <p>{error}</p>
        <Button 
          onClick={() => fetchWorkflows()} 
          variant="outline" 
          className="mt-4"
        >
          Try Again
        </Button>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        {[1, 2, 3].map((i) => (
          <Card key={i}>
            <CardHeader className="pb-2">
              <div className="flex justify-between items-center">
                <Skeleton className="h-6 w-1/3" />
                <Skeleton className="h-6 w-20" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-2/3" />
                <div className="flex flex-wrap gap-2 mt-4">
                  <Skeleton className="h-6 w-20 rounded-full" />
                  <Skeleton className="h-6 w-20 rounded-full" />
                </div>
                <div className="flex justify-between items-center mt-4">
                  <Skeleton className="h-4 w-1/4" />
                  <Skeleton className="h-8 w-20" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (workflows.length === 0) {
    return (
      <div className="p-12 border border-dashed rounded-lg flex flex-col items-center justify-center text-center">
        <h2 className="text-xl font-semibold mb-2">No Workflows Yet</h2>
        <p className="text-muted-foreground max-w-md mb-6">
          You haven't created any workflows yet. Get started by creating your first workflow.
        </p>
        <Button asChild>
          <Link href="/workflows/new">Create Your First Workflow</Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Filters and Search */}
      <div className="flex flex-col md:flex-row justify-between gap-4">
        <div className="flex flex-wrap gap-2">
          <Button 
            variant={filter === "all" ? "default" : "outline"} 
            size="sm"
            onClick={() => setFilter("all")}
          >
            All
          </Button>
          <Button 
            variant={filter === "instagram" ? "default" : "outline"} 
            size="sm"
            onClick={() => setFilter("instagram")}
          >
            Instagram
          </Button>
          <Button 
            variant={filter === "twitter" ? "default" : "outline"} 
            size="sm"
            onClick={() => setFilter("twitter")}
          >
            Twitter
          </Button>
          <Button 
            variant={filter === "facebook" ? "default" : "outline"} 
            size="sm"
            onClick={() => setFilter("facebook")}
          >
            Facebook
          </Button>
          <Button 
            variant={filter === "linkedin" ? "default" : "outline"} 
            size="sm"
            onClick={() => setFilter("linkedin")}
          >
            LinkedIn
          </Button>
          <Button 
            variant={filter === "youtube" ? "default" : "outline"} 
            size="sm"
            onClick={() => setFilter("youtube")}
          >
            YouTube
          </Button>
          <Button 
            variant={filter === "blog" ? "default" : "outline"} 
            size="sm"
            onClick={() => setFilter("blog")}
          >
            Blog
          </Button>
        </div>
        <div className="relative">
          <input
            type="text"
            placeholder="Search workflows..."
            className="w-full md:w-64 px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          {searchTerm && (
            <button
              className="absolute right-2 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
              onClick={() => setSearchTerm("")}
              aria-label="Clear search"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          )}
        </div>
      </div>

      {/* Workflow Items */}
      {filteredWorkflows.length === 0 ? (
        <div className="p-8 border border-dashed rounded-lg text-center">
          <p className="text-muted-foreground">No workflows match your filters.</p>
        </div>
      ) : (
        <div className="space-y-4">
          {filteredWorkflows.map((workflow) => (
            <Card key={workflow.id}>
              <CardHeader className="pb-2">
                <div className="flex justify-between items-center">
                  <CardTitle className="text-xl">{workflow.name}</CardTitle>
                  <div className="text-sm text-muted-foreground">
                    {workflow.steps.length} {workflow.steps.length === 1 ? "step" : "steps"}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground mb-4">{workflow.description}</p>
                <div className="flex flex-wrap gap-2 mb-4">
                  {workflow.platforms.map((platform) => (
                    <span
                      key={platform}
                      className="px-2 py-1 bg-muted rounded-full text-xs"
                    >
                      {platform}
                    </span>
                  ))}
                </div>
                <div className="flex justify-between items-center">
                  <div className="text-sm text-muted-foreground">
                    Last updated: {formatDate(workflow.updated_at)}
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      asChild
                    >
                      <Link href={`/workflows/${workflow.id}`}>View</Link>
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      asChild
                    >
                      <Link href={`/workflows/${workflow.id}/edit`}>Edit</Link>
                    </Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => handleDelete(workflow.id)}
                      disabled={isDeleting === workflow.id}
                    >
                      {isDeleting === workflow.id ? "Deleting..." : "Delete"}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
