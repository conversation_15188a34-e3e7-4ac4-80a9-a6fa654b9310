"use client";

import { useState } from "react";
import { Input } from "../ui/Input";
import { Button } from "../ui/Button";
import { WorkflowStep } from "../../context/WorkflowContext";

interface WorkflowStepEditorProps {
  step?: Omit<WorkflowStep, "id" | "position">;
  onSave: (step: Omit<WorkflowStep, "id" | "position">) => void;
  onCancel: () => void;
}

const STEP_TYPES = [
  {
    type: "content-analysis",
    name: "Content Analysis",
    description: "Analyze the content to identify key themes and topics",
  },
  {
    type: "platform-optimization",
    name: "Platform Optimization",
    description: "Optimize content for specific platforms",
  },
  {
    type: "scheduling",
    name: "Scheduling",
    description: "Schedule content for optimal times",
  },
  {
    type: "content-expansion",
    name: "Content Expansion",
    description: "Expand the content into a longer format",
  },
  {
    type: "seo-optimization",
    name: "SEO Optimization",
    description: "Optimize the content for search engines",
  },
  {
    type: "image-generation",
    name: "Image Generation",
    description: "Generate images for the content",
  },
  {
    type: "script-generation",
    name: "Script Generation",
    description: "Generate a script for video content",
  },
  {
    type: "thumbnail-creation",
    name: "Thumbnail Creation",
    description: "Create an eye-catching thumbnail",
  },
  {
    type: "tone-adjustment",
    name: "Tone Adjustment",
    description: "Adjust the tone of the content",
  },
  {
    type: "content-formatting",
    name: "Content Formatting",
    description: "Format the content for specific platforms",
  },
  {
    type: "custom",
    name: "Custom Step",
    description: "Create a custom workflow step",
  },
];

export default function WorkflowStepEditor({ step, onSave, onCancel }: WorkflowStepEditorProps) {
  const [stepType, setStepType] = useState(step?.type || "");
  const [name, setName] = useState(step?.name || "");
  const [description, setDescription] = useState(step?.description || "");
  const [config, setConfig] = useState<Record<string, any>>(step?.config || {});
  const [customType, setCustomType] = useState("");

  const handleTypeSelect = (type: string) => {
    setStepType(type);
    
    // Set default name and description based on type
    const selectedType = STEP_TYPES.find(t => t.type === type);
    if (selectedType) {
      setName(selectedType.name);
      setDescription(selectedType.description);
    }
  };

  const handleSave = () => {
    const finalType = stepType === "custom" ? customType : stepType;
    
    onSave({
      type: finalType,
      name,
      description,
      config,
    });
  };

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <label className="block text-sm font-medium">Step Type</label>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
          {STEP_TYPES.map((type) => (
            <button
              key={type.type}
              type="button"
              className={`px-3 py-2 rounded-md text-sm ${
                stepType === type.type
                  ? "bg-primary text-primary-foreground"
                  : "bg-muted hover:bg-muted/80"
              }`}
              onClick={() => handleTypeSelect(type.type)}
            >
              {type.name}
            </button>
          ))}
        </div>
      </div>

      {stepType === "custom" && (
        <Input
          id="customType"
          label="Custom Step Type"
          placeholder="Enter a custom step type"
          value={customType}
          onChange={(e) => setCustomType(e.target.value)}
          required
        />
      )}

      <Input
        id="name"
        label="Step Name"
        placeholder="Enter a name for this step"
        value={name}
        onChange={(e) => setName(e.target.value)}
        required
      />

      <div className="space-y-2">
        <label htmlFor="description" className="block text-sm font-medium">
          Description
        </label>
        <textarea
          id="description"
          className="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          rows={2}
          placeholder="Enter a brief description"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
        />
      </div>

      {/* Configuration options based on step type */}
      {stepType === "platform-optimization" && (
        <div className="space-y-2">
          <label className="block text-sm font-medium">Target Platforms</label>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
            {["instagram", "twitter", "facebook", "linkedin", "youtube", "blog"].map((platform) => (
              <button
                key={platform}
                type="button"
                className={`px-3 py-2 rounded-md text-sm ${
                  config.platforms?.includes(platform)
                    ? "bg-primary text-primary-foreground"
                    : "bg-muted hover:bg-muted/80"
                }`}
                onClick={() => {
                  const platforms = config.platforms || [];
                  if (platforms.includes(platform)) {
                    setConfig({
                      ...config,
                      platforms: platforms.filter(p => p !== platform),
                    });
                  } else {
                    setConfig({
                      ...config,
                      platforms: [...platforms, platform],
                    });
                  }
                }}
              >
                {platform.charAt(0).toUpperCase() + platform.slice(1)}
              </button>
            ))}
          </div>
        </div>
      )}

      {stepType === "scheduling" && (
        <div className="space-y-2">
          <label className="block text-sm font-medium">Timing Strategy</label>
          <div className="grid grid-cols-2 gap-2">
            <button
              type="button"
              className={`px-3 py-2 rounded-md text-sm ${
                config.timing === "optimal"
                  ? "bg-primary text-primary-foreground"
                  : "bg-muted hover:bg-muted/80"
              }`}
              onClick={() => setConfig({ ...config, timing: "optimal" })}
            >
              Optimal Time
            </button>
            <button
              type="button"
              className={`px-3 py-2 rounded-md text-sm ${
                config.timing === "immediate"
                  ? "bg-primary text-primary-foreground"
                  : "bg-muted hover:bg-muted/80"
              }`}
              onClick={() => setConfig({ ...config, timing: "immediate" })}
            >
              Immediate
            </button>
          </div>
        </div>
      )}

      {stepType === "content-expansion" && (
        <div className="space-y-2">
          <label className="block text-sm font-medium">Word Count Target</label>
          <div className="grid grid-cols-3 gap-2">
            <button
              type="button"
              className={`px-3 py-2 rounded-md text-sm ${
                config.wordCount === 500
                  ? "bg-primary text-primary-foreground"
                  : "bg-muted hover:bg-muted/80"
              }`}
              onClick={() => setConfig({ ...config, wordCount: 500 })}
            >
              Short (500)
            </button>
            <button
              type="button"
              className={`px-3 py-2 rounded-md text-sm ${
                config.wordCount === 1500
                  ? "bg-primary text-primary-foreground"
                  : "bg-muted hover:bg-muted/80"
              }`}
              onClick={() => setConfig({ ...config, wordCount: 1500 })}
            >
              Medium (1500)
            </button>
            <button
              type="button"
              className={`px-3 py-2 rounded-md text-sm ${
                config.wordCount === 3000
                  ? "bg-primary text-primary-foreground"
                  : "bg-muted hover:bg-muted/80"
              }`}
              onClick={() => setConfig({ ...config, wordCount: 3000 })}
            >
              Long (3000)
            </button>
          </div>
        </div>
      )}

      <div className="flex justify-end space-x-2 pt-4">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button 
          type="button" 
          onClick={handleSave}
          disabled={!name || (stepType === "custom" && !customType)}
        >
          Add Step
        </Button>
      </div>
    </div>
  );
}
