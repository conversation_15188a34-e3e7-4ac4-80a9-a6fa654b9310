"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "../ui/Card";
import { Skeleton } from "../ui/Skeleton";
import { formatDate } from "../../lib/utils";

interface RecentActivitiesProps {
  isLoading?: boolean;
}

// Mock data for recent activities
const mockActivities = [
  {
    id: "act1",
    type: "publish",
    title: "Summer Marketing Campaign",
    platform: "Instagram",
    date: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
  },
  {
    id: "act2",
    type: "create",
    title: "Product Launch Announcement",
    platform: "Blog",
    date: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
  },
  {
    id: "act3",
    type: "update",
    title: "Weekly Newsletter",
    platform: "Email",
    date: new Date(Date.now() - 1000 * 60 * 60 * 5), // 5 hours ago
  },
  {
    id: "act4",
    type: "publish",
    title: "Product Demo Video",
    platform: "YouTube",
    date: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
  },
];

export default function RecentActivities({ isLoading = false }: RecentActivitiesProps) {
  // Function to get icon based on activity type
  const getActivityIcon = (type: string) => {
    switch (type) {
      case "publish":
        return (
          <div className="rounded-full bg-primary/10 p-2 text-primary">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
        );
      case "create":
        return (
          <div className="rounded-full bg-secondary/10 p-2 text-secondary">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 6v6m0 0v6m0-6h6m-6 0H6"
              />
            </svg>
          </div>
        );
      case "update":
        return (
          <div className="rounded-full bg-accent/10 p-2 text-accent">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              />
            </svg>
          </div>
        );
      default:
        return (
          <div className="rounded-full bg-muted p-2">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
        );
    }
  };

  // Function to get platform badge
  const getPlatformBadge = (platform: string) => {
    const baseClasses = "px-2 py-1 rounded-full text-xs font-medium";
    
    switch (platform.toLowerCase()) {
      case "instagram":
        return <span className={`${baseClasses} bg-pink-100 text-pink-800`}>{platform}</span>;
      case "youtube":
        return <span className={`${baseClasses} bg-red-100 text-red-800`}>{platform}</span>;
      case "linkedin":
        return <span className={`${baseClasses} bg-blue-100 text-blue-800`}>{platform}</span>;
      case "blog":
        return <span className={`${baseClasses} bg-green-100 text-green-800`}>{platform}</span>;
      case "email":
        return <span className={`${baseClasses} bg-yellow-100 text-yellow-800`}>{platform}</span>;
      default:
        return <span className={`${baseClasses} bg-gray-100 text-gray-800`}>{platform}</span>;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Recent Activities</CardTitle>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-4">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="flex items-center space-x-4">
                <Skeleton className="h-10 w-10 rounded-full" />
                <div className="space-y-2 flex-1">
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="space-y-6">
            {mockActivities.map((activity) => (
              <div key={activity.id} className="flex items-start space-x-4">
                {getActivityIcon(activity.type)}
                <div className="flex-1 space-y-1">
                  <div className="flex items-center justify-between">
                    <p className="font-medium">{activity.title}</p>
                    <span className="text-xs text-muted-foreground">
                      {formatDate(activity.date)}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <p className="text-sm text-muted-foreground capitalize">
                      {activity.type}ed to {activity.platform}
                    </p>
                    {getPlatformBadge(activity.platform)}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
