"use client";

import { useEffect, useState } from "react";
import { createPortal } from "react-dom";
import { cn } from "../../lib/utils";

export type ToastProps = {
  id: string;
  title: string;
  description?: string;
  type?: "default" | "success" | "error" | "warning" | "info";
  duration?: number;
  onClose: (id: string) => void;
};

export function Toast({
  id,
  title,
  description,
  type = "default",
  duration = 5000,
  onClose,
}: ToastProps) {
  const [isVisible, setIsVisible] = useState(true);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
    return () => setIsMounted(false);
  }, []);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(false);
      setTimeout(() => onClose(id), 300); // Allow time for exit animation
    }, duration);

    return () => clearTimeout(timer);
  }, [duration, id, onClose]);

  const handleClose = () => {
    setIsVisible(false);
    setTimeout(() => onClose(id), 300);
  };

  const typeClasses = {
    default: "bg-background border-border",
    success: "bg-secondary/10 border-secondary text-secondary",
    error: "bg-error/10 border-error text-error",
    warning: "bg-yellow-500/10 border-yellow-500 text-yellow-500",
    info: "bg-primary/10 border-primary text-primary",
  };

  if (!isMounted) return null;

  return createPortal(
    <div
      className={cn(
        "fixed bottom-4 right-4 z-50 max-w-md rounded-lg border p-4 shadow-md transition-all duration-300",
        typeClasses[type],
        isVisible ? "translate-y-0 opacity-100" : "translate-y-2 opacity-0"
      )}
      role="alert"
    >
      <div className="flex items-start justify-between">
        <div>
          <h3 className="font-medium">{title}</h3>
          {description && <p className="mt-1 text-sm">{description}</p>}
        </div>
        <button
          onClick={handleClose}
          className="ml-4 inline-flex h-6 w-6 items-center justify-center rounded-full hover:bg-muted/50"
          aria-label="Close toast"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-4 w-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      </div>
    </div>,
    document.body
  );
}

type ToastContextType = {
  toasts: ToastProps[];
  addToast: (toast: Omit<ToastProps, "id" | "onClose">) => void;
  removeToast: (id: string) => void;
};

export function ToastContainer() {
  const [toasts, setToasts] = useState<ToastProps[]>([]);

  const addToast = (toast: Omit<ToastProps, "id" | "onClose">) => {
    const id = Math.random().toString(36).substring(2, 9);
    setToasts((prev) => [...prev, { ...toast, id, onClose: removeToast }]);
  };

  const removeToast = (id: string) => {
    setToasts((prev) => prev.filter((toast) => toast.id !== id));
  };

  return (
    <>
      {toasts.map((toast) => (
        <Toast key={toast.id} {...toast} />
      ))}
    </>
  );
}
