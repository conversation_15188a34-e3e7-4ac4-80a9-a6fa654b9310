/** @type {import('next').NextConfig} */
const nextConfig = {
  // Enable image optimization for dynamic images
  images: {
    domains: ['localhost'],
  },
  // Server configuration
  serverRuntimeConfig: {
    PROJECT_ROOT: __dirname,
  },
  // Enable server actions
  experimental: {
    serverActions: {
      bodySizeLimit: '2mb'
    },
  },
  // Disable ESLint during build to prevent build failures
  eslint: {
    // Warning: This allows production builds to successfully complete even if
    // your project has ESLint errors.
    ignoreDuringBuilds: true,
  },
  // Disable TypeScript type checking during build
  typescript: {
    // Warning: This allows production builds to successfully complete even if
    // your project has type errors.
    ignoreBuildErrors: true,
  },
};

module.exports = nextConfig;
