{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@radix-ui/react-slot": "^1.2.3", "@supabase/ssr": "^0.1.0", "@supabase/supabase-js": "^2.39.0", "@types/react-beautiful-dnd": "^13.1.8", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "next": "15.3.2", "picocolors": "^1.1.1", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.2.0", "recharts": "^2.15.3", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.4.21", "cssnano": "^6.0.1", "eslint": "^8.56.0", "eslint-config-next": "15.3.2", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.31", "postcss-nested": "^7.0.2", "tailwindcss": "^3.3.0", "typescript": "^5"}}