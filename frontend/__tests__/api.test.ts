/**
 * @jest-environment node
 */

import { fetchData } from '../lib/api';

describe('API Utilities', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('fetchData', () => {
    it('should return data when fetcher succeeds', async () => {
      const mockFetcher = jest.fn().mockResolvedValue({ data: 'test-data', error: null });
      const result = await fetchData(mockFetcher);

      expect(result).toEqual({
        data: 'test-data',
        error: null,
        status: 200,
      });
      expect(mockFetcher).toHaveBeenCalledTimes(1);
    });

    it('should return error when fetcher fails', async () => {
      const mockError = { message: 'Test error', status: 400 };
      const mockFetcher = jest.fn().mockResolvedValue({ data: null, error: mockError });
      const result = await fetchData(mockFetcher);

      expect(result).toEqual({
        data: null,
        error: new Error('Test error'),
        status: 400,
      });
      expect(mockFetcher).toHaveBeenCalledTimes(1);
    });

    it('should handle unexpected errors', async () => {
      const mockFetcher = jest.fn().mockRejectedValue(new Error('Unexpected error'));
      const result = await fetchData(mockFetcher);

      expect(result).toEqual({
        data: null,
        error: new Error('Unexpected error'),
        status: 500,
      });
      expect(mockFetcher).toHaveBeenCalledTimes(1);
    });
  });
});
