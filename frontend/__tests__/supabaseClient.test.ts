/**
 * @jest-environment node
 */

import { supabase, getCurrentUser, getSession } from '../lib/supabaseClient';

// Mock the Supabase client
jest.mock('@supabase/supabase-js', () => {
  const mockSession = {
    user: {
      id: 'test-user-id',
      email: '<EMAIL>',
      app_metadata: {},
      user_metadata: {},
      aud: 'authenticated',
      created_at: '2023-01-01T00:00:00.000Z',
    },
  };

  return {
    createClient: jest.fn(() => ({
      auth: {
        getSession: jest.fn().mockResolvedValue({ data: { session: mockSession } }),
        signInWithPassword: jest.fn().mockResolvedValue({ data: { user: mockSession.user, session: mockSession }, error: null }),
        signUp: jest.fn().mockResolvedValue({ data: { user: mockSession.user, session: null }, error: null }),
        signOut: jest.fn().mockResolvedValue({ error: null }),
        onAuthStateChange: jest.fn(() => ({
          data: {
            subscription: {
              unsubscribe: jest.fn(),
            },
          },
        })),
      },
      from: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      delete: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      order: jest.fn().mockReturnThis(),
      single: jest.fn().mockReturnThis(),
    })),
  };
});

describe('Supabase Client', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should export a Supabase client', () => {
    expect(supabase).toBeDefined();
    expect(supabase.auth).toBeDefined();
  });

  it('should get the current user', async () => {
    const user = await getCurrentUser();
    expect(user).toEqual({
      id: 'test-user-id',
      email: '<EMAIL>',
      app_metadata: {},
      user_metadata: {},
      aud: 'authenticated',
      created_at: '2023-01-01T00:00:00.000Z',
    });
  });

  it('should get the current session', async () => {
    const session = await getSession();
    expect(session).toEqual({
      user: {
        id: 'test-user-id',
        email: '<EMAIL>',
        app_metadata: {},
        user_metadata: {},
        aud: 'authenticated',
        created_at: '2023-01-01T00:00:00.000Z',
      },
    });
  });
});
