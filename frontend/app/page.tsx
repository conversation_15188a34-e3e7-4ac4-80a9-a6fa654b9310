import Image from "next/image";
import Link from "next/link";
import { Suspense } from "react";
import { AuthForm } from "./AuthForm";
import { BackendStatus } from "./BackendStatus";
import styles from "./page.module.css";

export default function Home() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="py-20 px-4 animated-gradient">
        <div className="container mx-auto max-w-6xl">
          <div className="flex flex-col items-center text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
              Streamline Your Content Creation Workflow
            </h1>
            <p className="text-xl text-white/90 max-w-3xl mb-10">
              ContentForge automates the creation, transformation, and publishing of your digital content across multiple platforms.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <Link href="/auth" className="btn btn-primary px-8 py-3 text-lg">
                Get Started
              </Link>
              <Link href="/demo" className="btn bg-white/20 text-white hover:bg-white/30 px-8 py-3 text-lg">
                Watch Demo
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-4 bg-background">
        <div className="container mx-auto max-w-6xl">
          <h2 className="text-3xl font-bold text-center mb-12">Key Features</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Feature 1 */}
            <div className={styles.feature}>
              <div className="rounded-full bg-primary/10 p-4 mb-4">
                <Image src="/file.svg" alt="Universal Input" width={32} height={32} />
              </div>
              <h3 className={styles.featureTitle}>Universal Input Portal</h3>
              <p className={styles.featureDescription}>
                Upload text, documents, images, or spreadsheets and transform them into optimized content for any platform.
              </p>
            </div>

            {/* Feature 2 */}
            <div className={styles.feature}>
              <div className="rounded-full bg-primary/10 p-4 mb-4">
                <Image src="/window.svg" alt="Workflow Orchestration" width={32} height={32} />
              </div>
              <h3 className={styles.featureTitle}>Intelligent Workflow Orchestration</h3>
              <p className={styles.featureDescription}>
                Create custom workflows to automate your content transformation and publishing process.
              </p>
            </div>

            {/* Feature 3 */}
            <div className={styles.feature}>
              <div className="rounded-full bg-primary/10 p-4 mb-4">
                <Image src="/globe.svg" alt="Multi-Platform Publishing" width={32} height={32} />
              </div>
              <h3 className={styles.featureTitle}>Multi-Platform Publishing</h3>
              <p className={styles.featureDescription}>
                Publish your content to YouTube, Instagram, LinkedIn, and blogs with platform-specific optimizations.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20 px-4 bg-muted/30">
        <div className="container mx-auto max-w-6xl">
          <h2 className="text-3xl font-bold text-center mb-12">How It Works</h2>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {/* Step 1 */}
            <div className="flex flex-col items-center text-center">
              <div className="rounded-full bg-primary text-white w-12 h-12 flex items-center justify-center text-xl font-bold mb-4">
                1
              </div>
              <h3 className="text-xl font-semibold mb-2">Input Content</h3>
              <p className="text-muted-foreground">
                Upload or create your content in any format.
              </p>
            </div>

            {/* Step 2 */}
            <div className="flex flex-col items-center text-center">
              <div className="rounded-full bg-primary text-white w-12 h-12 flex items-center justify-center text-xl font-bold mb-4">
                2
              </div>
              <h3 className="text-xl font-semibold mb-2">Select Workflow</h3>
              <p className="text-muted-foreground">
                Choose or create a workflow for your content.
              </p>
            </div>

            {/* Step 3 */}
            <div className="flex flex-col items-center text-center">
              <div className="rounded-full bg-primary text-white w-12 h-12 flex items-center justify-center text-xl font-bold mb-4">
                3
              </div>
              <h3 className="text-xl font-semibold mb-2">Transform</h3>
              <p className="text-muted-foreground">
                Let ContentForge optimize your content for each platform.
              </p>
            </div>

            {/* Step 4 */}
            <div className="flex flex-col items-center text-center">
              <div className="rounded-full bg-primary text-white w-12 h-12 flex items-center justify-center text-xl font-bold mb-4">
                4
              </div>
              <h3 className="text-xl font-semibold mb-2">Publish</h3>
              <p className="text-muted-foreground">
                Schedule or immediately publish across platforms.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 bg-primary/5">
        <div className="container mx-auto max-w-4xl text-center">
          <h2 className="text-3xl font-bold mb-6">Ready to Streamline Your Content Workflow?</h2>
          <p className="text-xl text-muted-foreground mb-10 max-w-2xl mx-auto">
            Join thousands of content creators, marketers, and businesses who are saving time and improving their digital presence with ContentForge.
          </p>
          <Link href="/auth" className="btn btn-primary px-8 py-3 text-lg">
            Get Started for Free
          </Link>
        </div>
      </section>

      {/* Auth Section */}
      <section className="py-20 px-4 bg-background">
        <div className="container mx-auto max-w-6xl">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold mb-6">Create Your Account</h2>
              <p className="text-xl text-muted-foreground mb-6">
                Sign up to start creating and publishing content across multiple platforms with ease.
              </p>
              <ul className="space-y-3">
                <li className="flex items-center">
                  <span className="mr-2 text-secondary">✓</span> No credit card required
                </li>
                <li className="flex items-center">
                  <span className="mr-2 text-secondary">✓</span> Free tier available
                </li>
                <li className="flex items-center">
                  <span className="mr-2 text-secondary">✓</span> Cancel anytime
                </li>
              </ul>
            </div>
            <div>
              <Suspense fallback={<div className="p-6 text-center">Loading authentication form...</div>}>
                <AuthForm />
              </Suspense>
            </div>
          </div>
        </div>
      </section>

      {/* Backend Status Section */}
      <section className="py-10 px-4 bg-muted/30">
        <div className="container mx-auto max-w-6xl">
          <h2 className="text-2xl font-bold text-center mb-8">System Status</h2>
          <Suspense fallback={<div className="p-6 text-center">Loading system status...</div>}>
            <BackendStatus />
          </Suspense>
        </div>
      </section>
    </div>
  );
}