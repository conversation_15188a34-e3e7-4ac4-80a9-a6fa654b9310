.authFormContainer {
  max-width: 320px;
  margin: 2rem auto;
  padding: 16px;
  border: 1px solid #eee;
  border-radius: 8px;
}
.authInput {
  width: 100%;
  margin-bottom: 8px;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}
.authButton {
  width: 100%;
  margin-bottom: 8px;
  padding: 8px;
  background-color: #0070f3;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}
.authButton:hover {
  background-color: #0060df;
}
.authButton:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}
.authButton:last-child {
  margin-bottom: 0;
}
.authError {
  color: #e00;
  font-size: 14px;
  margin-top: 8px;
}
.authSuccess {
  color: #0a0;
  font-size: 14px;
  margin-top: 8px;
}
