@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Base colors from frontend guidelines */
  --background: #ffffff;
  --foreground: #374151; /* Text color from guidelines */
  --primary: #3B82F6; /* Blue from guidelines */
  --primary-foreground: #ffffff;
  --secondary: #10B981; /* Green from guidelines */
  --secondary-foreground: #ffffff;
  --accent: #60a5fa;
  --accent-foreground: #ffffff;
  --muted: #f3f4f6;
  --muted-foreground: #6B7280; /* Secondary text from guidelines */
  --card: #ffffff;
  --card-foreground: #374151;
  --border: #e5e7eb;
  --input: #e5e7eb;
  --ring: #3B82F6;
  --error: #EF4444; /* Error color from guidelines */

  /* Typography */
  --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, 'Helvetica Neue', sans-serif;
  --font-mono: 'Geist Mono', monospace;

  /* Spacing */
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-12: 3rem;
  --spacing-16: 4rem;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
    --primary: #60a5fa;
    --primary-foreground: #171717;
    --secondary: #10B981;
    --secondary-foreground: #171717;
    --accent: #3b82f6;
    --accent-foreground: #171717;
    --muted: #1f2937;
    --muted-foreground: #9ca3af;
    --card: #171717;
    --card-foreground: #ededed;
    --border: #374151;
    --input: #374151;
    --ring: #60a5fa;
    --error: #EF4444;
  }
}

body {
  background-color: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans);
  font-feature-settings: "rlig" 1, "calt" 1;
  line-height: 1.5;
}

/* Typography styles based on frontend guidelines */
h1, .h1 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

h2, .h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
}

h3, .h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

h4, .h4 {
  font-size: 1.125rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

p {
  margin-bottom: 1rem;
}

small, .small {
  font-size: 0.875rem;
}

/* Button styles */
.btn {
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
  outline: none;
}
.btn:focus {
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

.btn-primary {
  background-color: var(--primary);
  color: var(--primary-foreground);
}
.btn-primary:hover {
  opacity: 0.9;
}
.btn-primary:focus {
  box-shadow: 0 0 0 2px var(--primary);
}

.btn-secondary {
  background-color: var(--secondary);
  color: var(--secondary-foreground);
}
.btn-secondary:hover {
  opacity: 0.9;
}
.btn-secondary:focus {
  box-shadow: 0 0 0 2px var(--secondary);
}

.btn-danger {
  background-color: var(--error);
  color: white;
}
.btn-danger:hover {
  opacity: 0.9;
}
.btn-danger:focus {
  box-shadow: 0 0 0 2px var(--error);
}

/* Form styles */
.form-input {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--input);
  border-radius: 0.375rem;
  outline: none;
}
.form-input:focus {
  box-shadow: 0 0 0 2px var(--primary);
  border-color: transparent;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.form-error {
  color: var(--error);
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.animated-gradient {
  background: linear-gradient(
    -45deg,
    var(--primary),
    var(--accent),
    var(--primary),
    var(--accent)
  );
  background-size: 400% 400%;
  animation: gradient 15s ease infinite;
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}