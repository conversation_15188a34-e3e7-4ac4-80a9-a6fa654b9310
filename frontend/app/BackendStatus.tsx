"use client";

import axios from "axios";
import { useEffect, useState } from "react";
import { Card, CardHeader, CardTitle, CardContent } from "../components/ui/Card";
import { Skeleton } from "../components/ui/Skeleton";

export function BackendStatus() {
  const [auth, setAuth] = useState<string | null>(null);
  const [inputs, setInputs] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const authRes = await axios.get("http://localhost:4000/auth");
        const inputsRes = await axios.get("http://localhost:4000/inputs");

        setAuth(authRes.data.message);
        setInputs(inputsRes.data.message);
        setError(null);
      } catch (err) {
        console.error("Error fetching backend status:", err);
        setError("Failed to connect to backend services");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  return (
    <Card className="w-full max-w-md mx-auto mt-8">
      <CardHeader>
        <CardTitle className="text-xl">Backend API Status</CardTitle>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="space-y-4">
            <Skeleton className="h-6 w-full" />
            <Skeleton className="h-6 w-full" />
          </div>
        ) : error ? (
          <div className="p-4 bg-error/10 border border-error text-error rounded-md">
            {error}
          </div>
        ) : (
          <div className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-muted/50 rounded-md">
              <span className="font-medium">Authentication Service:</span>
              <span className="px-2 py-1 bg-secondary/20 text-secondary rounded-md text-sm">
                {auth}
              </span>
            </div>
            <div className="flex items-center justify-between p-3 bg-muted/50 rounded-md">
              <span className="font-medium">Input Processing Service:</span>
              <span className="px-2 py-1 bg-secondary/20 text-secondary rounded-md text-sm">
                {inputs}
              </span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
