import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';

/**
 * This route handler manages the OAuth callback from Supabase Auth.
 * It exchanges the code for a session and redirects to the home page.
 *
 * Note: We're using the new @supabase/ssr package which replaces the deprecated
 * @supabase/auth-helpers-nextjs package for Next.js 15+.
 */
export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url);
  const code = requestUrl.searchParams.get('code');

  if (code) {
    try {
      const cookieStore = cookies();
      const supabase = createServerClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
        {
          cookies: {
            get: (name) => cookieStore.get(name)?.value,
            set: (name, value, options) => cookieStore.set(name, value, options),
            remove: (name, options) => cookieStore.set(name, '', { ...options, maxAge: 0 }),
          },
        }
      );
      await supabase.auth.exchangeCodeForSession(code);
    } catch (error) {
      console.error("Error during auth callback:", error);
      // Even if there's an error, we'll redirect to home
    }
  }

  // URL to redirect to after sign in process completes
  return NextResponse.redirect(new URL('/', request.url));
}
