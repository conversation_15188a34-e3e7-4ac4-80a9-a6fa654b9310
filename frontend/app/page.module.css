/* Pure CSS Module without Tailwind directives */
.container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

@media (min-width: 768px) {
  .container {
    padding: 5rem;
  }
}

.main {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
  max-width: 64rem;
  width: 100%;
}

.card {
  width: 100%;
  padding: 2rem;
  border-radius: 1rem;
  background-color: var(--card);
  color: var(--card-foreground);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid var(--border);
  transition: all 0.2s ease-in-out;
}

.card:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.title {
  font-size: 2.25rem;
  line-height: 2.5rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 1rem;
  background: linear-gradient(to right, var(--primary), var(--accent));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

@media (min-width: 768px) {
  .title {
    font-size: 3.75rem;
    line-height: 1;
  }
}

.description {
  font-size: 1.125rem;
  line-height: 1.75rem;
  text-align: center;
  color: var(--muted-foreground);
  margin-bottom: 2rem;
}

@media (min-width: 768px) {
  .description {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

.grid {
  display: grid;
  grid-template-columns: repeat(1, minmax(0, 1fr));
  gap: 1.5rem;
  width: 100%;
}

@media (min-width: 768px) {
  .grid {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (min-width: 1024px) {
  .grid {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

.feature {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  border-radius: 0.75rem;
  background-color: var(--muted);
  background-opacity: 0.5;
  transition: all 0.2s ease-in-out;
}

.feature:hover {
  background-color: var(--muted);
}

.featureTitle {
  font-size: 1.25rem;
  line-height: 1.75rem;
  font-weight: 600;
}

.featureDescription {
  font-size: 0.875rem;
  line-height: 1.25rem;
  color: var(--muted-foreground);
  text-align: center;
}

.cta {
  margin-top: 3rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
  justify-content: center;
}

@media (min-width: 640px) {
  .cta {
    flex-direction: row;
  }
}

.button {
  padding: 0.75rem 2rem;
  border-radius: 9999px;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
}

.primaryButton {
  composes: button;
  background-color: var(--primary);
  color: var(--primary-foreground);
}

.primaryButton:hover {
  background-color: var(--primary);
  opacity: 0.9;
}

.secondaryButton {
  composes: button;
  background-color: var(--secondary);
  color: var(--secondary-foreground);
}

.secondaryButton:hover {
  background-color: var(--secondary);
  opacity: 0.8;
}