# Supabase Integration

This directory contains the Supabase client integration and utility functions for the ContentForge application.

## Files

- `supabaseClient.ts`: Sets up the Supabase client with proper typing and authentication configuration.
- `api.ts`: Contains utility functions for making API calls to Supabase.

## Usage

### Authentication

The Supabase client is configured to handle authentication with session persistence and auto-refresh. You can use the authentication context to manage user sessions:

```tsx
import { useAuth } from "../hooks/useAuth";

function MyComponent() {
  const { user, signIn, signUp, signOut, isLoading } = useAuth();
  
  // Use these functions to manage authentication
}
```

### API Calls

The `api.ts` file provides utility functions for making API calls to Supabase:

```tsx
import { userApi, contentApi, workflowApi } from "../lib/api";

// Example: Get user profile
const { data, error } = await userApi.getProfile(userId);

// Example: Create new content input
const { data, error } = await contentApi.createInput({
  user_id: userId,
  input_type: "text",
  content: "My content"
});
```

## Environment Variables

The Supabase client requires the following environment variables:

- `NEXT_PUBLIC_SUPABASE_URL`: Your Supabase project URL
- `NEXT_PUBLIC_SUPABASE_ANON_KEY`: Your Supabase anonymous key

These should be set in your `.env.local` file.
