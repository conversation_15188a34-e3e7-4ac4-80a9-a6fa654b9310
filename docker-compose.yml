version: '3.8'
services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: contentforge-frontend
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
    volumes:
      - ./frontend:/app
      - /app/node_modules
    command: npm run dev
    depends_on:
      - backend

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: contentforge-backend
    ports:
      - "4000:4000"
    environment:
      - NODE_ENV=development
      - PORT=4000
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - N8N_URL=http://n8n:5678
    restart: unless-stopped
    volumes:
      - ./backend:/app
      - /app/node_modules
    command: sh -c "npm install && npm run dev"
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:4000/"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 5s
    depends_on:
      - n8n

  n8n:
    image: n8nio/n8n
    container_name: contentforge-n8n
    ports:
      - "5678:5678"
    environment:
      - GENERIC_TIMEZONE=Europe/London
      - N8N_PORT=5678
      - N8N_PROTOCOL=http
      - N8N_HOST=n8n
      - NODE_ENV=development
    volumes:
      - n8n_data:/home/<USER>/.n8n
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:5678/"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 10s

volumes:
  n8n_data:
