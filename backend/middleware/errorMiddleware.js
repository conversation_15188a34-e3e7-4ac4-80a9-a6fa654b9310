/**
 * Error handling middleware
 */

// Not Found middleware
exports.notFound = (req, res, next) => {
  const error = new Error(`Not Found - ${req.originalUrl}`);
  res.status(404);
  next(error);
};

// Error handler middleware
exports.errorHandler = (err, req, res, next) => {
  // Set status code (use the status code set by the route handler or default to 500)
  const statusCode = res.statusCode === 200 ? 500 : res.statusCode;
  
  // Set response status
  res.status(statusCode);
  
  // Send error response
  res.json({
    error: statusCode >= 500 ? 'Internal Server Error' : err.message,
    message: statusCode >= 500 && process.env.NODE_ENV === 'production' 
      ? 'An unexpected error occurred' 
      : err.message,
    stack: process.env.NODE_ENV === 'development' ? err.stack : undefined
  });
};
