const supabase = require('../config/supabase');

/**
 * Middleware to authenticate requests using Supabase JWT
 */
exports.authenticate = async (req, res, next) => {
  try {
    // Get the authorization header
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Unauthorized', message: 'Authentication token is required' });
    }
    
    // Extract the token
    const token = authHeader.split(' ')[1];
    
    // Verify the token with Supabase
    const { data: { user }, error } = await supabase.auth.getUser(token);
    
    if (error || !user) {
      return res.status(401).json({ error: 'Unauthorized', message: 'Invalid or expired token' });
    }
    
    // Attach the user to the request object
    req.user = user;
    
    // Continue to the next middleware or route handler
    next();
  } catch (error) {
    console.error('Authentication error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Authentication failed' });
  }
};

/**
 * Middleware to check if user has required role
 */
exports.authorize = (requiredRole) => {
  return async (req, res, next) => {
    try {
      // Get user from previous middleware
      const { user } = req;
      
      if (!user) {
        return res.status(401).json({ error: 'Unauthorized', message: 'User not authenticated' });
      }
      
      // Get user's role from Supabase
      const { data, error } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user.id)
        .single();
      
      if (error || !data) {
        return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to fetch user role' });
      }
      
      // Check if user has the required role
      if (data.role !== requiredRole && data.role !== 'admin') {
        return res.status(403).json({ error: 'Forbidden', message: 'Insufficient permissions' });
      }
      
      // User has the required role, continue
      next();
    } catch (error) {
      console.error('Authorization error:', error);
      return res.status(500).json({ error: 'Internal Server Error', message: 'Authorization failed' });
    }
  };
};
