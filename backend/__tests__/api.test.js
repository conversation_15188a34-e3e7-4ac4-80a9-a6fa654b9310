const request = require('supertest');
const app = require('../app');

describe('API Endpoints', () => {
  // Root endpoint
  describe('GET /', () => {
    it('should return a welcome message', async () => {
      const res = await request(app).get('/');
      expect(res.statusCode).toEqual(200);
      expect(res.body).toHaveProperty('message');
      expect(res.body.message).toEqual('ContentForge backend is running!');
    });
  });

  // User endpoints
  describe('User API', () => {
    it('should require authentication for protected routes', async () => {
      const res = await request(app).get('/api/users/profile');
      expect(res.statusCode).toEqual(401);
    });
  });

  // Content endpoints
  describe('Content API', () => {
    it('should require authentication for content routes', async () => {
      const res = await request(app).get('/api/content');
      expect(res.statusCode).toEqual(401);
    });
  });

  // Workflow endpoints
  describe('Workflow API', () => {
    it('should require authentication for workflow routes', async () => {
      const res = await request(app).get('/api/workflows');
      expect(res.statusCode).toEqual(401);
    });
  });

  // Publishing endpoints
  describe('Publishing API', () => {
    it('should require authentication for publishing routes', async () => {
      const res = await request(app).get('/api/publish');
      expect(res.statusCode).toEqual(401);
    });
  });

  // Analytics endpoints
  describe('Analytics API', () => {
    it('should require authentication for analytics routes', async () => {
      const res = await request(app).get('/api/analytics/overview');
      expect(res.statusCode).toEqual(401);
    });
  });
});
