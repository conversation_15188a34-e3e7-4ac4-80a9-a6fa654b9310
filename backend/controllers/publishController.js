const supabase = require('../config/supabase');

/**
 * Publish content to multiple platforms
 */
exports.publishContent = async (req, res) => {
  try {
    const { user } = req;
    const { content_id, platforms, schedule_time } = req.body;
    
    if (!content_id || !platforms || !Array.isArray(platforms) || platforms.length === 0) {
      return res.status(400).json({ 
        error: 'Bad Request', 
        message: 'Content ID and at least one platform are required' 
      });
    }
    
    // Check if content exists and belongs to the user
    const { data: content, error: contentError } = await supabase
      .from('content')
      .select('*')
      .eq('id', content_id)
      .single();
    
    if (contentError) {
      return res.status(404).json({ error: 'Not Found', message: 'Content not found' });
    }
    
    if (content.user_id !== user.id) {
      return res.status(403).json({ error: 'Forbidden', message: 'You do not have permission to publish this content' });
    }
    
    // If schedule_time is provided, schedule the publication
    if (schedule_time) {
      const scheduledTime = new Date(schedule_time);
      
      if (isNaN(scheduledTime.getTime())) {
        return res.status(400).json({ error: 'Bad Request', message: 'Invalid schedule time format' });
      }
      
      // Create scheduled publications
      const scheduledPublications = platforms.map(platform => ({
        content_id,
        user_id: user.id,
        platform,
        scheduled_time: scheduledTime,
        status: 'scheduled',
        created_at: new Date()
      }));
      
      const { data: scheduled, error: scheduleError } = await supabase
        .from('scheduled_publications')
        .insert(scheduledPublications)
        .select();
      
      if (scheduleError) {
        return res.status(400).json({ error: 'Scheduling Failed', message: scheduleError.message });
      }
      
      return res.status(201).json({ 
        message: 'Publication scheduled successfully',
        scheduled_publications: scheduled
      });
    } else {
      // Publish immediately
      const results = [];
      
      for (const platform of platforms) {
        // Create publication record
        const { data: publication, error: publicationError } = await supabase
          .from('published_content')
          .insert([
            {
              content_id,
              user_id: user.id,
              platform,
              publication_date: new Date(),
              status: 'processing'
            }
          ])
          .select()
          .single();
        
        if (publicationError) {
          console.error(`Error creating publication record for ${platform}:`, publicationError);
          results.push({
            platform,
            status: 'failed',
            error: publicationError.message
          });
          continue;
        }
        
        // Publish to platform (this would be implemented in platform-specific functions)
        let publishResult;
        try {
          switch (platform) {
            case 'youtube':
              publishResult = await this.publishToYouTube(req, res, content, publication.id);
              break;
            case 'instagram':
              publishResult = await this.publishToInstagram(req, res, content, publication.id);
              break;
            case 'linkedin':
              publishResult = await this.publishToLinkedIn(req, res, content, publication.id);
              break;
            case 'blog':
              publishResult = await this.publishToBlog(req, res, content, publication.id);
              break;
            default:
              publishResult = { success: false, error: `Unsupported platform: ${platform}` };
          }
          
          // Update publication record with result
          if (publishResult.success) {
            await supabase
              .from('published_content')
              .update({ 
                status: 'published',
                url: publishResult.url,
                platform_id: publishResult.platform_id
              })
              .eq('id', publication.id);
            
            results.push({
              platform,
              status: 'published',
              publication_id: publication.id,
              url: publishResult.url
            });
          } else {
            await supabase
              .from('published_content')
              .update({ 
                status: 'failed',
                error: publishResult.error
              })
              .eq('id', publication.id);
            
            results.push({
              platform,
              status: 'failed',
              error: publishResult.error
            });
          }
        } catch (error) {
          console.error(`Error publishing to ${platform}:`, error);
          
          await supabase
            .from('published_content')
            .update({ 
              status: 'failed',
              error: error.message
            })
            .eq('id', publication.id);
          
          results.push({
            platform,
            status: 'failed',
            error: error.message
          });
        }
      }
      
      return res.status(200).json({ 
        message: 'Publication process completed',
        results
      });
    }
  } catch (error) {
    console.error('Publish content error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to publish content' });
  }
};

/**
 * Get all published content for the user
 */
exports.getAllPublishedContent = async (req, res) => {
  try {
    const { user } = req;
    const { platform, status, limit = 10, page = 1 } = req.query;
    
    // Calculate offset for pagination
    const offset = (page - 1) * limit;
    
    // Build query
    let query = supabase
      .from('published_content')
      .select('*, content(*)')
      .eq('user_id', user.id)
      .order('publication_date', { ascending: false })
      .range(offset, offset + limit - 1);
    
    // Add filters if provided
    if (platform) {
      query = query.eq('platform', platform);
    }
    
    if (status) {
      query = query.eq('status', status);
    }
    
    // Execute query
    const { data, error } = await query;
    
    if (error) {
      return res.status(400).json({ error: 'Query Failed', message: error.message });
    }
    
    // Build count query with the same filters
    let countQuery = supabase
      .from('published_content')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id);
    
    if (platform) {
      countQuery = countQuery.eq('platform', platform);
    }
    
    if (status) {
      countQuery = countQuery.eq('status', status);
    }
    
    // Get total count for pagination
    const { count: totalCount, error: countError } = await countQuery;
    
    if (countError) {
      console.error('Count error:', countError);
    }
    
    return res.status(200).json({ 
      published_content: data,
      pagination: {
        total: totalCount || 0,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil((totalCount || 0) / limit)
      }
    });
  } catch (error) {
    console.error('Get all published content error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to fetch published content' });
  }
};

/**
 * Get published content by ID
 */
exports.getPublishedContentById = async (req, res) => {
  try {
    const { user } = req;
    const { id } = req.params;
    
    // Get published content from the database
    const { data, error } = await supabase
      .from('published_content')
      .select('*, content(*)')
      .eq('id', id)
      .single();
    
    if (error) {
      return res.status(404).json({ error: 'Not Found', message: 'Published content not found' });
    }
    
    // Check if the user owns the published content
    if (data.user_id !== user.id) {
      return res.status(403).json({ error: 'Forbidden', message: 'You do not have permission to access this published content' });
    }
    
    return res.status(200).json({ published_content: data });
  } catch (error) {
    console.error('Get published content error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to fetch published content' });
  }
};

/**
 * Unpublish content
 */
exports.unpublishContent = async (req, res) => {
  try {
    const { user } = req;
    const { id } = req.params;
    
    // Check if published content exists and belongs to the user
    const { data: publishedContent, error: fetchError } = await supabase
      .from('published_content')
      .select('*')
      .eq('id', id)
      .single();
    
    if (fetchError) {
      return res.status(404).json({ error: 'Not Found', message: 'Published content not found' });
    }
    
    if (publishedContent.user_id !== user.id) {
      return res.status(403).json({ error: 'Forbidden', message: 'You do not have permission to unpublish this content' });
    }
    
    // Unpublish from platform (this would be implemented in platform-specific functions)
    let unpublishResult;
    try {
      switch (publishedContent.platform) {
        case 'youtube':
          unpublishResult = await this.unpublishFromYouTube(publishedContent);
          break;
        case 'instagram':
          unpublishResult = await this.unpublishFromInstagram(publishedContent);
          break;
        case 'linkedin':
          unpublishResult = await this.unpublishFromLinkedIn(publishedContent);
          break;
        case 'blog':
          unpublishResult = await this.unpublishFromBlog(publishedContent);
          break;
        default:
          unpublishResult = { success: false, error: `Unsupported platform: ${publishedContent.platform}` };
      }
      
      if (!unpublishResult.success) {
        return res.status(400).json({ error: 'Unpublish Failed', message: unpublishResult.error });
      }
    } catch (error) {
      console.error(`Error unpublishing from ${publishedContent.platform}:`, error);
      return res.status(500).json({ error: 'Unpublish Failed', message: error.message });
    }
    
    // Update published content status
    const { error: updateError } = await supabase
      .from('published_content')
      .update({ 
        status: 'unpublished',
        unpublished_at: new Date()
      })
      .eq('id', id);
    
    if (updateError) {
      return res.status(400).json({ error: 'Update Failed', message: updateError.message });
    }
    
    return res.status(200).json({ message: 'Content unpublished successfully' });
  } catch (error) {
    console.error('Unpublish content error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to unpublish content' });
  }
};

/**
 * Publish to YouTube (mock implementation)
 */
exports.publishToYouTube = async (req, res, content, publicationId) => {
  // In a real implementation, this would use the YouTube API to publish the content
  // For now, we'll just return a mock success response
  
  // Mock implementation
  return {
    success: true,
    url: 'https://youtube.com/watch?v=mock-video-id',
    platform_id: 'mock-video-id'
  };
};

/**
 * Publish to Instagram (mock implementation)
 */
exports.publishToInstagram = async (req, res, content, publicationId) => {
  // In a real implementation, this would use the Instagram API to publish the content
  // For now, we'll just return a mock success response
  
  // Mock implementation
  return {
    success: true,
    url: 'https://instagram.com/p/mock-post-id',
    platform_id: 'mock-post-id'
  };
};

/**
 * Publish to LinkedIn (mock implementation)
 */
exports.publishToLinkedIn = async (req, res, content, publicationId) => {
  // In a real implementation, this would use the LinkedIn API to publish the content
  // For now, we'll just return a mock success response
  
  // Mock implementation
  return {
    success: true,
    url: 'https://linkedin.com/feed/update/mock-post-id',
    platform_id: 'mock-post-id'
  };
};

/**
 * Publish to Blog (mock implementation)
 */
exports.publishToBlog = async (req, res, content, publicationId) => {
  // In a real implementation, this would use a blog API (e.g., WordPress) to publish the content
  // For now, we'll just return a mock success response
  
  // Mock implementation
  return {
    success: true,
    url: 'https://example.com/blog/mock-post-slug',
    platform_id: 'mock-post-id'
  };
};

/**
 * Unpublish from YouTube (mock implementation)
 */
exports.unpublishFromYouTube = async (publishedContent) => {
  // In a real implementation, this would use the YouTube API to unpublish the content
  // For now, we'll just return a mock success response
  
  // Mock implementation
  return {
    success: true
  };
};

/**
 * Unpublish from Instagram (mock implementation)
 */
exports.unpublishFromInstagram = async (publishedContent) => {
  // In a real implementation, this would use the Instagram API to unpublish the content
  // For now, we'll just return a mock success response
  
  // Mock implementation
  return {
    success: true
  };
};

/**
 * Unpublish from LinkedIn (mock implementation)
 */
exports.unpublishFromLinkedIn = async (publishedContent) => {
  // In a real implementation, this would use the LinkedIn API to unpublish the content
  // For now, we'll just return a mock success response
  
  // Mock implementation
  return {
    success: true
  };
};

/**
 * Unpublish from Blog (mock implementation)
 */
exports.unpublishFromBlog = async (publishedContent) => {
  // In a real implementation, this would use a blog API (e.g., WordPress) to unpublish the content
  // For now, we'll just return a mock success response
  
  // Mock implementation
  return {
    success: true
  };
};

/**
 * Schedule publication
 */
exports.schedulePublication = async (req, res) => {
  try {
    const { user } = req;
    const { content_id, platforms, schedule_time } = req.body;
    
    if (!content_id || !platforms || !Array.isArray(platforms) || platforms.length === 0 || !schedule_time) {
      return res.status(400).json({ 
        error: 'Bad Request', 
        message: 'Content ID, platforms, and schedule time are required' 
      });
    }
    
    // Check if content exists and belongs to the user
    const { data: content, error: contentError } = await supabase
      .from('content')
      .select('*')
      .eq('id', content_id)
      .single();
    
    if (contentError) {
      return res.status(404).json({ error: 'Not Found', message: 'Content not found' });
    }
    
    if (content.user_id !== user.id) {
      return res.status(403).json({ error: 'Forbidden', message: 'You do not have permission to schedule this content' });
    }
    
    // Parse schedule time
    const scheduledTime = new Date(schedule_time);
    
    if (isNaN(scheduledTime.getTime())) {
      return res.status(400).json({ error: 'Bad Request', message: 'Invalid schedule time format' });
    }
    
    // Create scheduled publications
    const scheduledPublications = platforms.map(platform => ({
      content_id,
      user_id: user.id,
      platform,
      scheduled_time: scheduledTime,
      status: 'scheduled',
      created_at: new Date()
    }));
    
    const { data: scheduled, error: scheduleError } = await supabase
      .from('scheduled_publications')
      .insert(scheduledPublications)
      .select();
    
    if (scheduleError) {
      return res.status(400).json({ error: 'Scheduling Failed', message: scheduleError.message });
    }
    
    return res.status(201).json({ 
      message: 'Publication scheduled successfully',
      scheduled_publications: scheduled
    });
  } catch (error) {
    console.error('Schedule publication error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to schedule publication' });
  }
};

/**
 * Get scheduled publications
 */
exports.getScheduledPublications = async (req, res) => {
  try {
    const { user } = req;
    const { platform, limit = 10, page = 1 } = req.query;
    
    // Calculate offset for pagination
    const offset = (page - 1) * limit;
    
    // Build query
    let query = supabase
      .from('scheduled_publications')
      .select('*, content(*)')
      .eq('user_id', user.id)
      .order('scheduled_time', { ascending: true })
      .range(offset, offset + limit - 1);
    
    // Add platform filter if provided
    if (platform) {
      query = query.eq('platform', platform);
    }
    
    // Execute query
    const { data, error } = await query;
    
    if (error) {
      return res.status(400).json({ error: 'Query Failed', message: error.message });
    }
    
    // Build count query with the same filters
    let countQuery = supabase
      .from('scheduled_publications')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id);
    
    if (platform) {
      countQuery = countQuery.eq('platform', platform);
    }
    
    // Get total count for pagination
    const { count: totalCount, error: countError } = await countQuery;
    
    if (countError) {
      console.error('Count error:', countError);
    }
    
    return res.status(200).json({ 
      scheduled_publications: data,
      pagination: {
        total: totalCount || 0,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil((totalCount || 0) / limit)
      }
    });
  } catch (error) {
    console.error('Get scheduled publications error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to fetch scheduled publications' });
  }
};

/**
 * Update scheduled publication
 */
exports.updateScheduledPublication = async (req, res) => {
  try {
    const { user } = req;
    const { id } = req.params;
    const { schedule_time, platform } = req.body;
    
    // Check if scheduled publication exists and belongs to the user
    const { data: scheduledPublication, error: fetchError } = await supabase
      .from('scheduled_publications')
      .select('*')
      .eq('id', id)
      .single();
    
    if (fetchError) {
      return res.status(404).json({ error: 'Not Found', message: 'Scheduled publication not found' });
    }
    
    if (scheduledPublication.user_id !== user.id) {
      return res.status(403).json({ error: 'Forbidden', message: 'You do not have permission to update this scheduled publication' });
    }
    
    // Check if already published
    if (scheduledPublication.status !== 'scheduled') {
      return res.status(400).json({ 
        error: 'Bad Request', 
        message: `Cannot update publication with status '${scheduledPublication.status}'` 
      });
    }
    
    // Prepare update data
    const updateData = {};
    
    if (schedule_time) {
      const scheduledTime = new Date(schedule_time);
      
      if (isNaN(scheduledTime.getTime())) {
        return res.status(400).json({ error: 'Bad Request', message: 'Invalid schedule time format' });
      }
      
      updateData.scheduled_time = scheduledTime;
    }
    
    if (platform) {
      updateData.platform = platform;
    }
    
    if (Object.keys(updateData).length === 0) {
      return res.status(400).json({ error: 'Bad Request', message: 'No update data provided' });
    }
    
    // Update scheduled publication
    const { data, error } = await supabase
      .from('scheduled_publications')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();
    
    if (error) {
      return res.status(400).json({ error: 'Update Failed', message: error.message });
    }
    
    return res.status(200).json({ 
      message: 'Scheduled publication updated successfully',
      scheduled_publication: data
    });
  } catch (error) {
    console.error('Update scheduled publication error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to update scheduled publication' });
  }
};

/**
 * Cancel scheduled publication
 */
exports.cancelScheduledPublication = async (req, res) => {
  try {
    const { user } = req;
    const { id } = req.params;
    
    // Check if scheduled publication exists and belongs to the user
    const { data: scheduledPublication, error: fetchError } = await supabase
      .from('scheduled_publications')
      .select('*')
      .eq('id', id)
      .single();
    
    if (fetchError) {
      return res.status(404).json({ error: 'Not Found', message: 'Scheduled publication not found' });
    }
    
    if (scheduledPublication.user_id !== user.id) {
      return res.status(403).json({ error: 'Forbidden', message: 'You do not have permission to cancel this scheduled publication' });
    }
    
    // Check if already published
    if (scheduledPublication.status !== 'scheduled') {
      return res.status(400).json({ 
        error: 'Bad Request', 
        message: `Cannot cancel publication with status '${scheduledPublication.status}'` 
      });
    }
    
    // Delete scheduled publication
    const { error } = await supabase
      .from('scheduled_publications')
      .delete()
      .eq('id', id);
    
    if (error) {
      return res.status(400).json({ error: 'Cancellation Failed', message: error.message });
    }
    
    return res.status(200).json({ message: 'Scheduled publication cancelled successfully' });
  } catch (error) {
    console.error('Cancel scheduled publication error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to cancel scheduled publication' });
  }
};
