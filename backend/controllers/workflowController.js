const supabase = require('../config/supabase');
const axios = require('axios');

// n8n API URL
const n8nUrl = process.env.N8N_URL || 'http://localhost:5678';

/**
 * Create a new workflow
 */
exports.createWorkflow = async (req, res) => {
  try {
    const { user } = req;
    const { name, description, steps } = req.body;
    
    if (!name || !steps || !Array.isArray(steps)) {
      return res.status(400).json({ 
        error: 'Bad Request', 
        message: 'Name and steps array are required' 
      });
    }
    
    // Create workflow in the database
    const { data, error } = await supabase
      .from('workflows')
      .insert([
        {
          user_id: user.id,
          name,
          description,
          steps,
          created_at: new Date(),
          updated_at: new Date()
        }
      ])
      .select()
      .single();
    
    if (error) {
      return res.status(400).json({ error: 'Creation Failed', message: error.message });
    }
    
    return res.status(201).json({ 
      message: 'Workflow created successfully',
      workflow: data
    });
  } catch (error) {
    console.error('Create workflow error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to create workflow' });
  }
};

/**
 * Get all workflows for the user
 */
exports.getAllWorkflows = async (req, res) => {
  try {
    const { user } = req;
    const { limit = 10, page = 1 } = req.query;
    
    // Calculate offset for pagination
    const offset = (page - 1) * limit;
    
    // Get workflows from the database
    const { data, error } = await supabase
      .from('workflows')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);
    
    if (error) {
      return res.status(400).json({ error: 'Query Failed', message: error.message });
    }
    
    // Get total count for pagination
    const { count: totalCount, error: countError } = await supabase
      .from('workflows')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id);
    
    if (countError) {
      console.error('Count error:', countError);
    }
    
    return res.status(200).json({ 
      workflows: data,
      pagination: {
        total: totalCount || 0,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil((totalCount || 0) / limit)
      }
    });
  } catch (error) {
    console.error('Get all workflows error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to fetch workflows' });
  }
};

/**
 * Get workflow by ID
 */
exports.getWorkflowById = async (req, res) => {
  try {
    const { user } = req;
    const { id } = req.params;
    
    // Get workflow from the database
    const { data, error } = await supabase
      .from('workflows')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) {
      return res.status(404).json({ error: 'Not Found', message: 'Workflow not found' });
    }
    
    // Check if the user owns the workflow
    if (data.user_id !== user.id) {
      return res.status(403).json({ error: 'Forbidden', message: 'You do not have permission to access this workflow' });
    }
    
    return res.status(200).json({ workflow: data });
  } catch (error) {
    console.error('Get workflow error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to fetch workflow' });
  }
};

/**
 * Update workflow
 */
exports.updateWorkflow = async (req, res) => {
  try {
    const { user } = req;
    const { id } = req.params;
    const { name, description, steps } = req.body;
    
    // Check if workflow exists and belongs to the user
    const { data: existingWorkflow, error: fetchError } = await supabase
      .from('workflows')
      .select('user_id')
      .eq('id', id)
      .single();
    
    if (fetchError) {
      return res.status(404).json({ error: 'Not Found', message: 'Workflow not found' });
    }
    
    if (existingWorkflow.user_id !== user.id) {
      return res.status(403).json({ error: 'Forbidden', message: 'You do not have permission to update this workflow' });
    }
    
    // Update workflow in the database
    const { data, error } = await supabase
      .from('workflows')
      .update({ 
        name,
        description,
        steps,
        updated_at: new Date()
      })
      .eq('id', id)
      .select()
      .single();
    
    if (error) {
      return res.status(400).json({ error: 'Update Failed', message: error.message });
    }
    
    return res.status(200).json({ 
      message: 'Workflow updated successfully',
      workflow: data
    });
  } catch (error) {
    console.error('Update workflow error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to update workflow' });
  }
};

/**
 * Delete workflow
 */
exports.deleteWorkflow = async (req, res) => {
  try {
    const { user } = req;
    const { id } = req.params;
    
    // Check if workflow exists and belongs to the user
    const { data: existingWorkflow, error: fetchError } = await supabase
      .from('workflows')
      .select('user_id')
      .eq('id', id)
      .single();
    
    if (fetchError) {
      return res.status(404).json({ error: 'Not Found', message: 'Workflow not found' });
    }
    
    if (existingWorkflow.user_id !== user.id) {
      return res.status(403).json({ error: 'Forbidden', message: 'You do not have permission to delete this workflow' });
    }
    
    // Delete workflow from the database
    const { error } = await supabase
      .from('workflows')
      .delete()
      .eq('id', id);
    
    if (error) {
      return res.status(400).json({ error: 'Deletion Failed', message: error.message });
    }
    
    return res.status(200).json({ message: 'Workflow deleted successfully' });
  } catch (error) {
    console.error('Delete workflow error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to delete workflow' });
  }
};

/**
 * Execute workflow
 */
exports.executeWorkflow = async (req, res) => {
  try {
    const { user } = req;
    const { id } = req.params;
    const { content_id, parameters } = req.body;
    
    if (!content_id) {
      return res.status(400).json({ error: 'Bad Request', message: 'Content ID is required' });
    }
    
    // Check if workflow exists and belongs to the user
    const { data: workflow, error: workflowError } = await supabase
      .from('workflows')
      .select('*')
      .eq('id', id)
      .single();
    
    if (workflowError) {
      return res.status(404).json({ error: 'Not Found', message: 'Workflow not found' });
    }
    
    if (workflow.user_id !== user.id) {
      return res.status(403).json({ error: 'Forbidden', message: 'You do not have permission to execute this workflow' });
    }
    
    // Check if content exists and belongs to the user
    const { data: content, error: contentError } = await supabase
      .from('content')
      .select('*')
      .eq('id', content_id)
      .single();
    
    if (contentError) {
      return res.status(404).json({ error: 'Not Found', message: 'Content not found' });
    }
    
    if (content.user_id !== user.id) {
      return res.status(403).json({ error: 'Forbidden', message: 'You do not have permission to use this content' });
    }
    
    // Create workflow execution record
    const { data: execution, error: executionError } = await supabase
      .from('workflow_executions')
      .insert([
        {
          workflow_id: id,
          content_id,
          user_id: user.id,
          status: 'pending',
          parameters,
          started_at: new Date()
        }
      ])
      .select()
      .single();
    
    if (executionError) {
      return res.status(400).json({ error: 'Execution Failed', message: executionError.message });
    }
    
    // Execute workflow in n8n (this is a simplified example)
    try {
      // In a real implementation, you would trigger the workflow in n8n
      // and handle the response asynchronously
      
      // Mock n8n API call
      /*
      const n8nResponse = await axios.post(`${n8nUrl}/api/v1/workflows/${workflow.n8n_workflow_id}/execute`, {
        data: {
          content,
          parameters,
          execution_id: execution.id
        }
      });
      */
      
      // For now, we'll just update the execution status to 'running'
      await supabase
        .from('workflow_executions')
        .update({ status: 'running' })
        .eq('id', execution.id);
      
      return res.status(202).json({ 
        message: 'Workflow execution started',
        execution_id: execution.id,
        status: 'running'
      });
    } catch (n8nError) {
      console.error('n8n execution error:', n8nError);
      
      // Update execution status to 'failed'
      await supabase
        .from('workflow_executions')
        .update({ 
          status: 'failed',
          error: n8nError.message,
          completed_at: new Date()
        })
        .eq('id', execution.id);
      
      return res.status(500).json({ error: 'Execution Failed', message: 'Failed to execute workflow in n8n' });
    }
  } catch (error) {
    console.error('Execute workflow error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to execute workflow' });
  }
};

/**
 * Get workflow execution status
 */
exports.getWorkflowStatus = async (req, res) => {
  try {
    const { user } = req;
    const { id } = req.params;
    const { execution_id } = req.query;
    
    if (!execution_id) {
      // Get all executions for this workflow
      const { data, error } = await supabase
        .from('workflow_executions')
        .select('*')
        .eq('workflow_id', id)
        .eq('user_id', user.id)
        .order('started_at', { ascending: false })
        .limit(10);
      
      if (error) {
        return res.status(400).json({ error: 'Query Failed', message: error.message });
      }
      
      return res.status(200).json({ executions: data });
    } else {
      // Get specific execution
      const { data, error } = await supabase
        .from('workflow_executions')
        .select('*')
        .eq('id', execution_id)
        .eq('user_id', user.id)
        .single();
      
      if (error) {
        return res.status(404).json({ error: 'Not Found', message: 'Execution not found' });
      }
      
      return res.status(200).json({ execution: data });
    }
  } catch (error) {
    console.error('Get workflow status error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to get workflow status' });
  }
};

/**
 * Cancel workflow execution
 */
exports.cancelWorkflow = async (req, res) => {
  try {
    const { user } = req;
    const { id } = req.params;
    const { execution_id } = req.body;
    
    if (!execution_id) {
      return res.status(400).json({ error: 'Bad Request', message: 'Execution ID is required' });
    }
    
    // Check if execution exists and belongs to the user
    const { data: execution, error: executionError } = await supabase
      .from('workflow_executions')
      .select('*')
      .eq('id', execution_id)
      .eq('user_id', user.id)
      .single();
    
    if (executionError) {
      return res.status(404).json({ error: 'Not Found', message: 'Execution not found' });
    }
    
    // Check if execution is in a cancellable state
    if (execution.status !== 'pending' && execution.status !== 'running') {
      return res.status(400).json({ 
        error: 'Bad Request', 
        message: `Cannot cancel execution with status '${execution.status}'` 
      });
    }
    
    // Cancel execution in n8n (this is a simplified example)
    try {
      // In a real implementation, you would cancel the workflow in n8n
      // and handle the response asynchronously
      
      // Mock n8n API call
      /*
      await axios.post(`${n8nUrl}/api/v1/workflows/${execution.n8n_execution_id}/cancel`);
      */
      
      // Update execution status to 'cancelled'
      await supabase
        .from('workflow_executions')
        .update({ 
          status: 'cancelled',
          completed_at: new Date()
        })
        .eq('id', execution_id);
      
      return res.status(200).json({ 
        message: 'Workflow execution cancelled',
        execution_id
      });
    } catch (n8nError) {
      console.error('n8n cancellation error:', n8nError);
      return res.status(500).json({ error: 'Cancellation Failed', message: 'Failed to cancel workflow in n8n' });
    }
  } catch (error) {
    console.error('Cancel workflow error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to cancel workflow' });
  }
};

/**
 * Get workflow templates
 */
exports.getWorkflowTemplates = async (req, res) => {
  try {
    // Get workflow templates from the database
    const { data, error } = await supabase
      .from('workflow_templates')
      .select('*')
      .order('name', { ascending: true });
    
    if (error) {
      return res.status(400).json({ error: 'Query Failed', message: error.message });
    }
    
    return res.status(200).json({ templates: data });
  } catch (error) {
    console.error('Get workflow templates error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to fetch workflow templates' });
  }
};

/**
 * Clone workflow template
 */
exports.cloneWorkflowTemplate = async (req, res) => {
  try {
    const { user } = req;
    const { templateId } = req.params;
    const { name, description } = req.body;
    
    // Get template from the database
    const { data: template, error: templateError } = await supabase
      .from('workflow_templates')
      .select('*')
      .eq('id', templateId)
      .single();
    
    if (templateError) {
      return res.status(404).json({ error: 'Not Found', message: 'Template not found' });
    }
    
    // Create new workflow based on template
    const { data, error } = await supabase
      .from('workflows')
      .insert([
        {
          user_id: user.id,
          name: name || `${template.name} (Copy)`,
          description: description || template.description,
          steps: template.steps,
          created_at: new Date(),
          updated_at: new Date(),
          cloned_from: templateId
        }
      ])
      .select()
      .single();
    
    if (error) {
      return res.status(400).json({ error: 'Clone Failed', message: error.message });
    }
    
    return res.status(201).json({ 
      message: 'Workflow template cloned successfully',
      workflow: data
    });
  } catch (error) {
    console.error('Clone workflow template error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to clone workflow template' });
  }
};
