const supabase = require('../config/supabase');

/**
 * Get analytics for a specific content
 */
exports.getContentAnalytics = async (req, res) => {
  try {
    const { user } = req;
    const { contentId } = req.params;
    const { start_date, end_date } = req.query;
    
    // Check if content exists and belongs to the user
    const { data: content, error: contentError } = await supabase
      .from('content')
      .select('user_id')
      .eq('id', contentId)
      .single();
    
    if (contentError) {
      return res.status(404).json({ error: 'Not Found', message: 'Content not found' });
    }
    
    if (content.user_id !== user.id) {
      return res.status(403).json({ error: 'Forbidden', message: 'You do not have permission to access analytics for this content' });
    }
    
    // Build query to get published content
    let publishedQuery = supabase
      .from('published_content')
      .select('id, platform, publication_date, url')
      .eq('content_id', contentId);
    
    // Execute query
    const { data: publishedContent, error: publishedError } = await publishedQuery;
    
    if (publishedError) {
      return res.status(400).json({ error: 'Query Failed', message: publishedError.message });
    }
    
    // If no published content, return empty analytics
    if (!publishedContent || publishedContent.length === 0) {
      return res.status(200).json({ 
        content_id: contentId,
        analytics: [],
        summary: {
          total_views: 0,
          total_likes: 0,
          total_shares: 0,
          total_comments: 0
        }
      });
    }
    
    // Get analytics for each published content
    const analyticsPromises = publishedContent.map(async (publication) => {
      // Build analytics query
      let analyticsQuery = supabase
        .from('analytics')
        .select('*')
        .eq('published_content_id', publication.id)
        .order('date', { ascending: false });
      
      // Add date filters if provided
      if (start_date) {
        analyticsQuery = analyticsQuery.gte('date', start_date);
      }
      
      if (end_date) {
        analyticsQuery = analyticsQuery.lte('date', end_date);
      }
      
      // Execute query
      const { data: analytics, error: analyticsError } = await analyticsQuery;
      
      if (analyticsError) {
        console.error(`Error fetching analytics for publication ${publication.id}:`, analyticsError);
        return {
          platform: publication.platform,
          publication_id: publication.id,
          url: publication.url,
          analytics: []
        };
      }
      
      return {
        platform: publication.platform,
        publication_id: publication.id,
        url: publication.url,
        analytics: analytics || []
      };
    });
    
    const analyticsResults = await Promise.all(analyticsPromises);
    
    // Calculate summary metrics
    const summary = analyticsResults.reduce((acc, result) => {
      const platformMetrics = result.analytics.reduce((platformAcc, analytic) => {
        const metrics = analytic.metrics || {};
        
        return {
          views: (platformAcc.views || 0) + (metrics.views || 0),
          likes: (platformAcc.likes || 0) + (metrics.likes || 0),
          shares: (platformAcc.shares || 0) + (metrics.shares || 0),
          comments: (platformAcc.comments || 0) + (metrics.comments || 0)
        };
      }, {});
      
      return {
        total_views: acc.total_views + (platformMetrics.views || 0),
        total_likes: acc.total_likes + (platformMetrics.likes || 0),
        total_shares: acc.total_shares + (platformMetrics.shares || 0),
        total_comments: acc.total_comments + (platformMetrics.comments || 0)
      };
    }, { total_views: 0, total_likes: 0, total_shares: 0, total_comments: 0 });
    
    return res.status(200).json({ 
      content_id: contentId,
      analytics: analyticsResults,
      summary
    });
  } catch (error) {
    console.error('Get content analytics error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to fetch content analytics' });
  }
};

/**
 * Get analytics for a specific platform
 */
exports.getPlatformAnalytics = async (req, res) => {
  try {
    const { user } = req;
    const { platform } = req.params;
    const { start_date, end_date, limit = 10, page = 1 } = req.query;
    
    // Validate platform
    const validPlatforms = ['youtube', 'instagram', 'linkedin', 'blog'];
    if (!validPlatforms.includes(platform)) {
      return res.status(400).json({ error: 'Bad Request', message: 'Invalid platform' });
    }
    
    // Calculate offset for pagination
    const offset = (page - 1) * limit;
    
    // Get published content for the platform
    let publishedQuery = supabase
      .from('published_content')
      .select('id, content_id, publication_date, url, content(title)')
      .eq('user_id', user.id)
      .eq('platform', platform)
      .order('publication_date', { ascending: false })
      .range(offset, offset + limit - 1);
    
    // Execute query
    const { data: publishedContent, error: publishedError } = await publishedQuery;
    
    if (publishedError) {
      return res.status(400).json({ error: 'Query Failed', message: publishedError.message });
    }
    
    // If no published content, return empty analytics
    if (!publishedContent || publishedContent.length === 0) {
      return res.status(200).json({ 
        platform,
        analytics: [],
        summary: {
          total_views: 0,
          total_likes: 0,
          total_shares: 0,
          total_comments: 0
        },
        pagination: {
          total: 0,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: 0
        }
      });
    }
    
    // Get analytics for each published content
    const analyticsPromises = publishedContent.map(async (publication) => {
      // Build analytics query
      let analyticsQuery = supabase
        .from('analytics')
        .select('*')
        .eq('published_content_id', publication.id)
        .order('date', { ascending: false });
      
      // Add date filters if provided
      if (start_date) {
        analyticsQuery = analyticsQuery.gte('date', start_date);
      }
      
      if (end_date) {
        analyticsQuery = analyticsQuery.lte('date', end_date);
      }
      
      // Execute query
      const { data: analytics, error: analyticsError } = await analyticsQuery;
      
      if (analyticsError) {
        console.error(`Error fetching analytics for publication ${publication.id}:`, analyticsError);
        return {
          content_id: publication.content_id,
          content_title: publication.content?.title,
          publication_id: publication.id,
          url: publication.url,
          publication_date: publication.publication_date,
          analytics: []
        };
      }
      
      // Calculate summary for this publication
      const publicationSummary = (analytics || []).reduce((acc, analytic) => {
        const metrics = analytic.metrics || {};
        
        return {
          views: (acc.views || 0) + (metrics.views || 0),
          likes: (acc.likes || 0) + (metrics.likes || 0),
          shares: (acc.shares || 0) + (metrics.shares || 0),
          comments: (acc.comments || 0) + (metrics.comments || 0)
        };
      }, {});
      
      return {
        content_id: publication.content_id,
        content_title: publication.content?.title,
        publication_id: publication.id,
        url: publication.url,
        publication_date: publication.publication_date,
        analytics: analytics || [],
        summary: publicationSummary
      };
    });
    
    const analyticsResults = await Promise.all(analyticsPromises);
    
    // Calculate overall summary metrics
    const summary = analyticsResults.reduce((acc, result) => {
      const summary = result.summary || {};
      
      return {
        total_views: acc.total_views + (summary.views || 0),
        total_likes: acc.total_likes + (summary.likes || 0),
        total_shares: acc.total_shares + (summary.shares || 0),
        total_comments: acc.total_comments + (summary.comments || 0)
      };
    }, { total_views: 0, total_likes: 0, total_shares: 0, total_comments: 0 });
    
    // Get total count for pagination
    const { count: totalCount, error: countError } = await supabase
      .from('published_content')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id)
      .eq('platform', platform);
    
    if (countError) {
      console.error('Count error:', countError);
    }
    
    return res.status(200).json({ 
      platform,
      analytics: analyticsResults,
      summary,
      pagination: {
        total: totalCount || 0,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil((totalCount || 0) / limit)
      }
    });
  } catch (error) {
    console.error('Get platform analytics error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to fetch platform analytics' });
  }
};

/**
 * Get analytics overview for all platforms
 */
exports.getAnalyticsOverview = async (req, res) => {
  try {
    const { user } = req;
    const { start_date, end_date } = req.query;
    
    // Define platforms
    const platforms = ['youtube', 'instagram', 'linkedin', 'blog'];
    
    // Get analytics for each platform
    const platformPromises = platforms.map(async (platform) => {
      // Get published content for the platform
      let publishedQuery = supabase
        .from('published_content')
        .select('id')
        .eq('user_id', user.id)
        .eq('platform', platform);
      
      // Execute query
      const { data: publishedContent, error: publishedError } = await publishedQuery;
      
      if (publishedError) {
        console.error(`Error fetching published content for ${platform}:`, publishedError);
        return {
          platform,
          total_publications: 0,
          summary: {
            views: 0,
            likes: 0,
            shares: 0,
            comments: 0
          }
        };
      }
      
      // If no published content, return empty analytics
      if (!publishedContent || publishedContent.length === 0) {
        return {
          platform,
          total_publications: 0,
          summary: {
            views: 0,
            likes: 0,
            shares: 0,
            comments: 0
          }
        };
      }
      
      // Get publication IDs
      const publicationIds = publishedContent.map(p => p.id);
      
      // Build analytics query
      let analyticsQuery = supabase
        .from('analytics')
        .select('published_content_id, metrics')
        .in('published_content_id', publicationIds);
      
      // Add date filters if provided
      if (start_date) {
        analyticsQuery = analyticsQuery.gte('date', start_date);
      }
      
      if (end_date) {
        analyticsQuery = analyticsQuery.lte('date', end_date);
      }
      
      // Execute query
      const { data: analytics, error: analyticsError } = await analyticsQuery;
      
      if (analyticsError) {
        console.error(`Error fetching analytics for ${platform}:`, analyticsError);
        return {
          platform,
          total_publications: publishedContent.length,
          summary: {
            views: 0,
            likes: 0,
            shares: 0,
            comments: 0
          }
        };
      }
      
      // Calculate summary metrics
      const summary = (analytics || []).reduce((acc, analytic) => {
        const metrics = analytic.metrics || {};
        
        return {
          views: acc.views + (metrics.views || 0),
          likes: acc.likes + (metrics.likes || 0),
          shares: acc.shares + (metrics.shares || 0),
          comments: acc.comments + (metrics.comments || 0)
        };
      }, { views: 0, likes: 0, shares: 0, comments: 0 });
      
      return {
        platform,
        total_publications: publishedContent.length,
        summary
      };
    });
    
    const platformResults = await Promise.all(platformPromises);
    
    // Calculate overall summary
    const overallSummary = platformResults.reduce((acc, platform) => {
      return {
        total_views: acc.total_views + platform.summary.views,
        total_likes: acc.total_likes + platform.summary.likes,
        total_shares: acc.total_shares + platform.summary.shares,
        total_comments: acc.total_comments + platform.summary.comments,
        total_publications: acc.total_publications + platform.total_publications
      };
    }, { total_views: 0, total_likes: 0, total_shares: 0, total_comments: 0, total_publications: 0 });
    
    return res.status(200).json({ 
      platforms: platformResults,
      summary: overallSummary
    });
  } catch (error) {
    console.error('Get analytics overview error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to fetch analytics overview' });
  }
};

/**
 * Get performance metrics over time
 */
exports.getPerformanceMetrics = async (req, res) => {
  try {
    const { user } = req;
    const { platform, metric, interval = 'day', start_date, end_date } = req.query;
    
    // Validate metric
    const validMetrics = ['views', 'likes', 'shares', 'comments'];
    if (!metric || !validMetrics.includes(metric)) {
      return res.status(400).json({ error: 'Bad Request', message: 'Valid metric is required (views, likes, shares, comments)' });
    }
    
    // Validate interval
    const validIntervals = ['day', 'week', 'month'];
    if (!validIntervals.includes(interval)) {
      return res.status(400).json({ error: 'Bad Request', message: 'Valid interval is required (day, week, month)' });
    }
    
    // Build query to get published content
    let publishedQuery = supabase
      .from('published_content')
      .select('id')
      .eq('user_id', user.id);
    
    // Add platform filter if provided
    if (platform) {
      publishedQuery = publishedQuery.eq('platform', platform);
    }
    
    // Execute query
    const { data: publishedContent, error: publishedError } = await publishedQuery;
    
    if (publishedError) {
      return res.status(400).json({ error: 'Query Failed', message: publishedError.message });
    }
    
    // If no published content, return empty metrics
    if (!publishedContent || publishedContent.length === 0) {
      return res.status(200).json({ 
        metric,
        interval,
        data: []
      });
    }
    
    // Get publication IDs
    const publicationIds = publishedContent.map(p => p.id);
    
    // Build analytics query
    let analyticsQuery = supabase
      .from('analytics')
      .select('date, metrics')
      .in('published_content_id', publicationIds)
      .order('date', { ascending: true });
    
    // Add date filters if provided
    if (start_date) {
      analyticsQuery = analyticsQuery.gte('date', start_date);
    }
    
    if (end_date) {
      analyticsQuery = analyticsQuery.lte('date', end_date);
    }
    
    // Execute query
    const { data: analytics, error: analyticsError } = await analyticsQuery;
    
    if (analyticsError) {
      return res.status(400).json({ error: 'Query Failed', message: analyticsError.message });
    }
    
    // Group analytics by interval
    const groupedData = {};
    
    analytics.forEach(analytic => {
      const date = new Date(analytic.date);
      let intervalKey;
      
      // Create interval key based on selected interval
      switch (interval) {
        case 'day':
          intervalKey = date.toISOString().split('T')[0]; // YYYY-MM-DD
          break;
        case 'week':
          // Get the first day of the week (Sunday)
          const firstDayOfWeek = new Date(date);
          const day = date.getDay();
          const diff = date.getDate() - day;
          firstDayOfWeek.setDate(diff);
          intervalKey = firstDayOfWeek.toISOString().split('T')[0];
          break;
        case 'month':
          intervalKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
          break;
      }
      
      // Initialize interval if not exists
      if (!groupedData[intervalKey]) {
        groupedData[intervalKey] = 0;
      }
      
      // Add metric value
      groupedData[intervalKey] += (analytic.metrics && analytic.metrics[metric]) || 0;
    });
    
    // Convert to array format
    const result = Object.entries(groupedData).map(([date, value]) => ({
      date,
      [metric]: value
    }));
    
    return res.status(200).json({ 
      metric,
      interval,
      data: result
    });
  } catch (error) {
    console.error('Get performance metrics error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to fetch performance metrics' });
  }
};

/**
 * Get analytics by date range
 */
exports.getAnalyticsByDateRange = async (req, res) => {
  try {
    const { user } = req;
    const { start_date, end_date, platform } = req.query;
    
    if (!start_date || !end_date) {
      return res.status(400).json({ error: 'Bad Request', message: 'Start date and end date are required' });
    }
    
    // Parse dates
    const startDate = new Date(start_date);
    const endDate = new Date(end_date);
    
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      return res.status(400).json({ error: 'Bad Request', message: 'Invalid date format' });
    }
    
    // Build query to get published content
    let publishedQuery = supabase
      .from('published_content')
      .select('id, platform')
      .eq('user_id', user.id);
    
    // Add platform filter if provided
    if (platform) {
      publishedQuery = publishedQuery.eq('platform', platform);
    }
    
    // Execute query
    const { data: publishedContent, error: publishedError } = await publishedQuery;
    
    if (publishedError) {
      return res.status(400).json({ error: 'Query Failed', message: publishedError.message });
    }
    
    // If no published content, return empty analytics
    if (!publishedContent || publishedContent.length === 0) {
      return res.status(200).json({ 
        start_date,
        end_date,
        platform: platform || 'all',
        analytics: []
      });
    }
    
    // Get publication IDs
    const publicationIds = publishedContent.map(p => p.id);
    
    // Build analytics query
    let analyticsQuery = supabase
      .from('analytics')
      .select('*')
      .in('published_content_id', publicationIds)
      .gte('date', startDate.toISOString())
      .lte('date', endDate.toISOString())
      .order('date', { ascending: true });
    
    // Execute query
    const { data: analytics, error: analyticsError } = await analyticsQuery;
    
    if (analyticsError) {
      return res.status(400).json({ error: 'Query Failed', message: analyticsError.message });
    }
    
    // Group analytics by platform
    const platformMap = {};
    publishedContent.forEach(p => {
      platformMap[p.id] = p.platform;
    });
    
    const groupedByPlatform = {};
    
    analytics.forEach(analytic => {
      const platform = platformMap[analytic.published_content_id];
      
      if (!groupedByPlatform[platform]) {
        groupedByPlatform[platform] = [];
      }
      
      groupedByPlatform[platform].push(analytic);
    });
    
    // Calculate summary for each platform
    const platformSummaries = {};
    
    Object.entries(groupedByPlatform).forEach(([platform, analytics]) => {
      const summary = analytics.reduce((acc, analytic) => {
        const metrics = analytic.metrics || {};
        
        return {
          views: acc.views + (metrics.views || 0),
          likes: acc.likes + (metrics.likes || 0),
          shares: acc.shares + (metrics.shares || 0),
          comments: acc.comments + (metrics.comments || 0)
        };
      }, { views: 0, likes: 0, shares: 0, comments: 0 });
      
      platformSummaries[platform] = summary;
    });
    
    // Calculate overall summary
    const overallSummary = Object.values(platformSummaries).reduce((acc, summary) => {
      return {
        total_views: acc.total_views + summary.views,
        total_likes: acc.total_likes + summary.likes,
        total_shares: acc.total_shares + summary.shares,
        total_comments: acc.total_comments + summary.comments
      };
    }, { total_views: 0, total_likes: 0, total_shares: 0, total_comments: 0 });
    
    return res.status(200).json({ 
      start_date,
      end_date,
      platform: platform || 'all',
      analytics: Object.entries(groupedByPlatform).map(([platform, analytics]) => ({
        platform,
        data: analytics,
        summary: platformSummaries[platform]
      })),
      summary: overallSummary
    });
  } catch (error) {
    console.error('Get analytics by date range error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to fetch analytics by date range' });
  }
};

/**
 * Export analytics data
 */
exports.exportAnalytics = async (req, res) => {
  try {
    const { user } = req;
    const { platform, start_date, end_date, format = 'json' } = req.query;
    
    // Validate format
    const validFormats = ['json', 'csv'];
    if (!validFormats.includes(format)) {
      return res.status(400).json({ error: 'Bad Request', message: 'Invalid format (must be json or csv)' });
    }
    
    // Build query to get published content
    let publishedQuery = supabase
      .from('published_content')
      .select('id, platform, content_id, url, content(title)')
      .eq('user_id', user.id);
    
    // Add platform filter if provided
    if (platform) {
      publishedQuery = publishedQuery.eq('platform', platform);
    }
    
    // Execute query
    const { data: publishedContent, error: publishedError } = await publishedQuery;
    
    if (publishedError) {
      return res.status(400).json({ error: 'Query Failed', message: publishedError.message });
    }
    
    // If no published content, return empty data
    if (!publishedContent || publishedContent.length === 0) {
      if (format === 'csv') {
        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', 'attachment; filename="analytics.csv"');
        return res.status(200).send('platform,content_title,url,date,views,likes,shares,comments\n');
      } else {
        return res.status(200).json({ data: [] });
      }
    }
    
    // Get publication IDs
    const publicationIds = publishedContent.map(p => p.id);
    
    // Build analytics query
    let analyticsQuery = supabase
      .from('analytics')
      .select('*')
      .in('published_content_id', publicationIds)
      .order('date', { ascending: true });
    
    // Add date filters if provided
    if (start_date) {
      analyticsQuery = analyticsQuery.gte('date', start_date);
    }
    
    if (end_date) {
      analyticsQuery = analyticsQuery.lte('date', end_date);
    }
    
    // Execute query
    const { data: analytics, error: analyticsError } = await analyticsQuery;
    
    if (analyticsError) {
      return res.status(400).json({ error: 'Query Failed', message: analyticsError.message });
    }
    
    // Create a map of publication details
    const publicationMap = {};
    publishedContent.forEach(p => {
      publicationMap[p.id] = {
        platform: p.platform,
        content_title: p.content?.title || 'Untitled',
        url: p.url
      };
    });
    
    // Format data for export
    const exportData = analytics.map(analytic => {
      const publication = publicationMap[analytic.published_content_id] || {};
      const metrics = analytic.metrics || {};
      
      return {
        platform: publication.platform || 'unknown',
        content_title: publication.content_title || 'Untitled',
        url: publication.url || '',
        date: analytic.date,
        views: metrics.views || 0,
        likes: metrics.likes || 0,
        shares: metrics.shares || 0,
        comments: metrics.comments || 0
      };
    });
    
    // Return data in requested format
    if (format === 'csv') {
      // Generate CSV
      const header = 'platform,content_title,url,date,views,likes,shares,comments\n';
      const rows = exportData.map(item => 
        `${item.platform},"${item.content_title.replace(/"/g, '""')}",${item.url},${item.date},${item.views},${item.likes},${item.shares},${item.comments}`
      ).join('\n');
      
      const csv = header + rows;
      
      // Set headers for CSV download
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', 'attachment; filename="analytics.csv"');
      
      return res.status(200).send(csv);
    } else {
      // Return JSON
      return res.status(200).json({ data: exportData });
    }
  } catch (error) {
    console.error('Export analytics error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to export analytics' });
  }
};
