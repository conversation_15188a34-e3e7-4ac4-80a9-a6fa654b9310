const supabase = require('../config/supabase');

/**
 * Register a new user
 */
exports.register = async (req, res) => {
  try {
    const { email, password, name } = req.body;
    
    if (!email || !password) {
      return res.status(400).json({ error: 'Bad Request', message: 'Email and password are required' });
    }
    
    // Register user with Supabase Auth
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email,
      password,
    });
    
    if (authError) {
      return res.status(400).json({ error: 'Registration Failed', message: authError.message });
    }
    
    // Create user profile in the profiles table
    if (authData.user) {
      const { error: profileError } = await supabase
        .from('profiles')
        .insert([
          { 
            id: authData.user.id, 
            name: name || email.split('@')[0], 
            email,
            role: 'user',
            created_at: new Date()
          }
        ]);
      
      if (profileError) {
        console.error('Error creating user profile:', profileError);
        // We don't return an error here as the auth user was created successfully
      }
    }
    
    return res.status(201).json({ 
      message: 'User registered successfully',
      user: {
        id: authData.user.id,
        email: authData.user.email
      }
    });
  } catch (error) {
    console.error('Registration error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Registration failed' });
  }
};

/**
 * Login a user
 */
exports.login = async (req, res) => {
  try {
    const { email, password } = req.body;
    
    if (!email || !password) {
      return res.status(400).json({ error: 'Bad Request', message: 'Email and password are required' });
    }
    
    // Sign in user with Supabase Auth
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    
    if (error) {
      return res.status(401).json({ error: 'Authentication Failed', message: error.message });
    }
    
    return res.status(200).json({ 
      message: 'Login successful',
      user: {
        id: data.user.id,
        email: data.user.email
      },
      session: {
        access_token: data.session.access_token,
        expires_at: data.session.expires_at
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Login failed' });
  }
};

/**
 * Get user profile
 */
exports.getProfile = async (req, res) => {
  try {
    const { user } = req;
    
    // Get user profile from the profiles table
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single();
    
    if (error) {
      return res.status(404).json({ error: 'Not Found', message: 'User profile not found' });
    }
    
    // Remove sensitive information
    const { password, ...profile } = data;
    
    return res.status(200).json({ profile });
  } catch (error) {
    console.error('Get profile error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to fetch profile' });
  }
};

/**
 * Update user profile
 */
exports.updateProfile = async (req, res) => {
  try {
    const { user } = req;
    const { name, bio, avatar_url } = req.body;
    
    // Update user profile in the profiles table
    const { data, error } = await supabase
      .from('profiles')
      .update({ 
        name, 
        bio, 
        avatar_url,
        updated_at: new Date()
      })
      .eq('id', user.id)
      .select()
      .single();
    
    if (error) {
      return res.status(400).json({ error: 'Update Failed', message: error.message });
    }
    
    return res.status(200).json({ 
      message: 'Profile updated successfully',
      profile: data
    });
  } catch (error) {
    console.error('Update profile error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to update profile' });
  }
};

/**
 * Forgot password
 */
exports.forgotPassword = async (req, res) => {
  try {
    const { email } = req.body;
    
    if (!email) {
      return res.status(400).json({ error: 'Bad Request', message: 'Email is required' });
    }
    
    // Send password reset email
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${process.env.FRONTEND_URL}/reset-password`,
    });
    
    if (error) {
      return res.status(400).json({ error: 'Request Failed', message: error.message });
    }
    
    return res.status(200).json({ message: 'Password reset email sent' });
  } catch (error) {
    console.error('Forgot password error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to send reset email' });
  }
};

/**
 * Reset password
 */
exports.resetPassword = async (req, res) => {
  try {
    const { password } = req.body;
    
    if (!password) {
      return res.status(400).json({ error: 'Bad Request', message: 'New password is required' });
    }
    
    // Update user password
    const { error } = await supabase.auth.updateUser({
      password
    });
    
    if (error) {
      return res.status(400).json({ error: 'Reset Failed', message: error.message });
    }
    
    return res.status(200).json({ message: 'Password reset successful' });
  } catch (error) {
    console.error('Reset password error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to reset password' });
  }
};

/**
 * Get user preferences
 */
exports.getPreferences = async (req, res) => {
  try {
    const { user } = req;
    
    // Get user preferences from the profiles table
    const { data, error } = await supabase
      .from('profiles')
      .select('preferences')
      .eq('id', user.id)
      .single();
    
    if (error) {
      return res.status(404).json({ error: 'Not Found', message: 'User preferences not found' });
    }
    
    return res.status(200).json({ preferences: data.preferences || {} });
  } catch (error) {
    console.error('Get preferences error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to fetch preferences' });
  }
};

/**
 * Update user preferences
 */
exports.updatePreferences = async (req, res) => {
  try {
    const { user } = req;
    const { preferences } = req.body;
    
    if (!preferences || typeof preferences !== 'object') {
      return res.status(400).json({ error: 'Bad Request', message: 'Valid preferences object is required' });
    }
    
    // Update user preferences in the profiles table
    const { data, error } = await supabase
      .from('profiles')
      .update({ 
        preferences,
        updated_at: new Date()
      })
      .eq('id', user.id)
      .select('preferences')
      .single();
    
    if (error) {
      return res.status(400).json({ error: 'Update Failed', message: error.message });
    }
    
    return res.status(200).json({ 
      message: 'Preferences updated successfully',
      preferences: data.preferences
    });
  } catch (error) {
    console.error('Update preferences error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to update preferences' });
  }
};
