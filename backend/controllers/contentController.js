const supabase = require('../config/supabase');

/**
 * Create new content
 */
exports.createContent = async (req, res) => {
  try {
    const { user } = req;
    const { title, input_type, input_data, description, tags } = req.body;
    
    if (!title || !input_type || !input_data) {
      return res.status(400).json({ 
        error: 'Bad Request', 
        message: 'Title, input type, and input data are required' 
      });
    }
    
    // Create content in the database
    const { data, error } = await supabase
      .from('content')
      .insert([
        {
          user_id: user.id,
          title,
          input_type,
          input_data,
          description,
          tags,
          status: 'draft',
          created_at: new Date(),
          updated_at: new Date()
        }
      ])
      .select()
      .single();
    
    if (error) {
      return res.status(400).json({ error: 'Creation Failed', message: error.message });
    }
    
    return res.status(201).json({ 
      message: 'Content created successfully',
      content: data
    });
  } catch (error) {
    console.error('Create content error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to create content' });
  }
};

/**
 * Get all content for the user
 */
exports.getAllContent = async (req, res) => {
  try {
    const { user } = req;
    const { status, limit = 10, page = 1 } = req.query;
    
    // Calculate offset for pagination
    const offset = (page - 1) * limit;
    
    // Build query
    let query = supabase
      .from('content')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);
    
    // Add status filter if provided
    if (status) {
      query = query.eq('status', status);
    }
    
    // Execute query
    const { data, error, count } = await query;
    
    if (error) {
      return res.status(400).json({ error: 'Query Failed', message: error.message });
    }
    
    // Get total count for pagination
    const { count: totalCount, error: countError } = await supabase
      .from('content')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id);
    
    if (countError) {
      console.error('Count error:', countError);
    }
    
    return res.status(200).json({ 
      content: data,
      pagination: {
        total: totalCount || 0,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil((totalCount || 0) / limit)
      }
    });
  } catch (error) {
    console.error('Get all content error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to fetch content' });
  }
};

/**
 * Get content by ID
 */
exports.getContentById = async (req, res) => {
  try {
    const { user } = req;
    const { id } = req.params;
    
    // Get content from the database
    const { data, error } = await supabase
      .from('content')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) {
      return res.status(404).json({ error: 'Not Found', message: 'Content not found' });
    }
    
    // Check if the user owns the content
    if (data.user_id !== user.id) {
      return res.status(403).json({ error: 'Forbidden', message: 'You do not have permission to access this content' });
    }
    
    return res.status(200).json({ content: data });
  } catch (error) {
    console.error('Get content error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to fetch content' });
  }
};

/**
 * Update content
 */
exports.updateContent = async (req, res) => {
  try {
    const { user } = req;
    const { id } = req.params;
    const { title, input_type, input_data, description, tags, status } = req.body;
    
    // Check if content exists and belongs to the user
    const { data: existingContent, error: fetchError } = await supabase
      .from('content')
      .select('user_id')
      .eq('id', id)
      .single();
    
    if (fetchError) {
      return res.status(404).json({ error: 'Not Found', message: 'Content not found' });
    }
    
    if (existingContent.user_id !== user.id) {
      return res.status(403).json({ error: 'Forbidden', message: 'You do not have permission to update this content' });
    }
    
    // Update content in the database
    const { data, error } = await supabase
      .from('content')
      .update({ 
        title,
        input_type,
        input_data,
        description,
        tags,
        status,
        updated_at: new Date()
      })
      .eq('id', id)
      .select()
      .single();
    
    if (error) {
      return res.status(400).json({ error: 'Update Failed', message: error.message });
    }
    
    return res.status(200).json({ 
      message: 'Content updated successfully',
      content: data
    });
  } catch (error) {
    console.error('Update content error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to update content' });
  }
};

/**
 * Delete content
 */
exports.deleteContent = async (req, res) => {
  try {
    const { user } = req;
    const { id } = req.params;
    
    // Check if content exists and belongs to the user
    const { data: existingContent, error: fetchError } = await supabase
      .from('content')
      .select('user_id')
      .eq('id', id)
      .single();
    
    if (fetchError) {
      return res.status(404).json({ error: 'Not Found', message: 'Content not found' });
    }
    
    if (existingContent.user_id !== user.id) {
      return res.status(403).json({ error: 'Forbidden', message: 'You do not have permission to delete this content' });
    }
    
    // Delete content from the database
    const { error } = await supabase
      .from('content')
      .delete()
      .eq('id', id);
    
    if (error) {
      return res.status(400).json({ error: 'Deletion Failed', message: error.message });
    }
    
    return res.status(200).json({ message: 'Content deleted successfully' });
  } catch (error) {
    console.error('Delete content error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to delete content' });
  }
};

/**
 * Search content
 */
exports.searchContent = async (req, res) => {
  try {
    const { user } = req;
    const { query, limit = 10, page = 1 } = req.query;
    
    if (!query) {
      return res.status(400).json({ error: 'Bad Request', message: 'Search query is required' });
    }
    
    // Calculate offset for pagination
    const offset = (page - 1) * limit;
    
    // Search content using Supabase text search
    const { data, error } = await supabase
      .from('content')
      .select('*')
      .eq('user_id', user.id)
      .or(`title.ilike.%${query}%,description.ilike.%${query}%`)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);
    
    if (error) {
      return res.status(400).json({ error: 'Search Failed', message: error.message });
    }
    
    // Get total count for pagination
    const { count: totalCount, error: countError } = await supabase
      .from('content')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id)
      .or(`title.ilike.%${query}%,description.ilike.%${query}%`);
    
    if (countError) {
      console.error('Count error:', countError);
    }
    
    return res.status(200).json({ 
      content: data,
      pagination: {
        total: totalCount || 0,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil((totalCount || 0) / limit)
      }
    });
  } catch (error) {
    console.error('Search content error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to search content' });
  }
};

/**
 * Filter content
 */
exports.filterContent = async (req, res) => {
  try {
    const { user } = req;
    const { status, input_type, tags, start_date, end_date, limit = 10, page = 1 } = req.query;
    
    // Calculate offset for pagination
    const offset = (page - 1) * limit;
    
    // Build query
    let query = supabase
      .from('content')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });
    
    // Add filters if provided
    if (status) {
      query = query.eq('status', status);
    }
    
    if (input_type) {
      query = query.eq('input_type', input_type);
    }
    
    if (tags) {
      // Filter by tags (assuming tags is stored as an array)
      query = query.contains('tags', [tags]);
    }
    
    if (start_date) {
      query = query.gte('created_at', start_date);
    }
    
    if (end_date) {
      query = query.lte('created_at', end_date);
    }
    
    // Add pagination
    query = query.range(offset, offset + limit - 1);
    
    // Execute query
    const { data, error } = await query;
    
    if (error) {
      return res.status(400).json({ error: 'Filter Failed', message: error.message });
    }
    
    // Build count query with the same filters
    let countQuery = supabase
      .from('content')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id);
    
    if (status) {
      countQuery = countQuery.eq('status', status);
    }
    
    if (input_type) {
      countQuery = countQuery.eq('input_type', input_type);
    }
    
    if (tags) {
      countQuery = countQuery.contains('tags', [tags]);
    }
    
    if (start_date) {
      countQuery = countQuery.gte('created_at', start_date);
    }
    
    if (end_date) {
      countQuery = countQuery.lte('created_at', end_date);
    }
    
    // Get total count for pagination
    const { count: totalCount, error: countError } = await countQuery;
    
    if (countError) {
      console.error('Count error:', countError);
    }
    
    return res.status(200).json({ 
      content: data,
      pagination: {
        total: totalCount || 0,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil((totalCount || 0) / limit)
      }
    });
  } catch (error) {
    console.error('Filter content error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to filter content' });
  }
};

/**
 * Get content versions
 */
exports.getContentVersions = async (req, res) => {
  try {
    const { user } = req;
    const { id } = req.params;
    
    // Check if content exists and belongs to the user
    const { data: existingContent, error: fetchError } = await supabase
      .from('content')
      .select('user_id')
      .eq('id', id)
      .single();
    
    if (fetchError) {
      return res.status(404).json({ error: 'Not Found', message: 'Content not found' });
    }
    
    if (existingContent.user_id !== user.id) {
      return res.status(403).json({ error: 'Forbidden', message: 'You do not have permission to access this content' });
    }
    
    // Get content versions from the database
    const { data, error } = await supabase
      .from('content_versions')
      .select('*')
      .eq('content_id', id)
      .order('created_at', { ascending: false });
    
    if (error) {
      return res.status(400).json({ error: 'Query Failed', message: error.message });
    }
    
    return res.status(200).json({ versions: data });
  } catch (error) {
    console.error('Get content versions error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to fetch content versions' });
  }
};

/**
 * Get specific content version
 */
exports.getContentVersion = async (req, res) => {
  try {
    const { user } = req;
    const { id, versionId } = req.params;
    
    // Check if content exists and belongs to the user
    const { data: existingContent, error: fetchError } = await supabase
      .from('content')
      .select('user_id')
      .eq('id', id)
      .single();
    
    if (fetchError) {
      return res.status(404).json({ error: 'Not Found', message: 'Content not found' });
    }
    
    if (existingContent.user_id !== user.id) {
      return res.status(403).json({ error: 'Forbidden', message: 'You do not have permission to access this content' });
    }
    
    // Get specific content version from the database
    const { data, error } = await supabase
      .from('content_versions')
      .select('*')
      .eq('id', versionId)
      .eq('content_id', id)
      .single();
    
    if (error) {
      return res.status(404).json({ error: 'Not Found', message: 'Content version not found' });
    }
    
    return res.status(200).json({ version: data });
  } catch (error) {
    console.error('Get content version error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to fetch content version' });
  }
};
