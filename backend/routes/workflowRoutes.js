const express = require('express');
const router = express.Router();
const workflowController = require('../controllers/workflowController');
const authMiddleware = require('../middleware/authMiddleware');

// All workflow routes require authentication
router.use(authMiddleware.authenticate);

// Workflow CRUD operations
router.post('/', workflowController.createWorkflow);
router.get('/', workflowController.getAllWorkflows);
router.get('/:id', workflowController.getWorkflowById);
router.put('/:id', workflowController.updateWorkflow);
router.delete('/:id', workflowController.deleteWorkflow);

// Workflow execution
router.post('/:id/execute', workflowController.executeWorkflow);
router.get('/:id/status', workflowController.getWorkflowStatus);
router.post('/:id/cancel', workflowController.cancelWorkflow);

// Workflow templates
router.get('/templates', workflowController.getWorkflowTemplates);
router.post('/templates/:templateId/clone', workflowController.cloneWorkflowTemplate);

module.exports = router;
