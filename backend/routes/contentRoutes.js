const express = require('express');
const router = express.Router();
const contentController = require('../controllers/contentController');
const authMiddleware = require('../middleware/authMiddleware');

// All content routes require authentication
router.use(authMiddleware.authenticate);

// Content CRUD operations
router.post('/', contentController.createContent);
router.get('/', contentController.getAllContent);
router.get('/:id', contentController.getContentById);
router.put('/:id', contentController.updateContent);
router.delete('/:id', contentController.deleteContent);

// Content search and filtering
router.get('/search', contentController.searchContent);
router.get('/filter', contentController.filterContent);

// Content versioning
router.get('/:id/versions', contentController.getContentVersions);
router.get('/:id/versions/:versionId', contentController.getContentVersion);

module.exports = router;
