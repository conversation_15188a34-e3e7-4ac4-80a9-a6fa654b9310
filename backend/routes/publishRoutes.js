const express = require('express');
const router = express.Router();
const publishController = require('../controllers/publishController');
const authMiddleware = require('../middleware/authMiddleware');

// All publishing routes require authentication
router.use(authMiddleware.authenticate);

// Publishing operations
router.post('/', publishController.publishContent);
router.get('/', publishController.getAllPublishedContent);
router.get('/:id', publishController.getPublishedContentById);
router.delete('/:id', publishController.unpublishContent);

// Platform-specific operations
router.post('/youtube', publishController.publishToYouTube);
router.post('/instagram', publishController.publishToInstagram);
router.post('/linkedin', publishController.publishToLinkedIn);
router.post('/blog', publishController.publishToBlog);

// Scheduling
router.post('/schedule', publishController.schedulePublication);
router.get('/schedule', publishController.getScheduledPublications);
router.put('/schedule/:id', publishController.updateScheduledPublication);
router.delete('/schedule/:id', publishController.cancelScheduledPublication);

module.exports = router;
