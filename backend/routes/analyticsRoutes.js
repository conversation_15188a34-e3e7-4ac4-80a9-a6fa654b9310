const express = require('express');
const router = express.Router();
const analyticsController = require('../controllers/analyticsController');
const authMiddleware = require('../middleware/authMiddleware');

// All analytics routes require authentication
router.use(authMiddleware.authenticate);

// Analytics operations
router.get('/content/:contentId', analyticsController.getContentAnalytics);
router.get('/platform/:platform', analyticsController.getPlatformAnalytics);
router.get('/overview', analyticsController.getAnalyticsOverview);
router.get('/performance', analyticsController.getPerformanceMetrics);

// Date range filtering
router.get('/range', analyticsController.getAnalyticsByDateRange);

// Export analytics
router.get('/export', analyticsController.exportAnalytics);

module.exports = router;
