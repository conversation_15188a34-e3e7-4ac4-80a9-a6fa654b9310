const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
require('dotenv').config();

// Import middleware
const { notFound, errorHandler } = require('./middleware/errorMiddleware');

// Import routes
const userRoutes = require('./routes/userRoutes');
const contentRoutes = require('./routes/contentRoutes');
const workflowRoutes = require('./routes/workflowRoutes');
const publishRoutes = require('./routes/publishRoutes');
const analyticsRoutes = require('./routes/analyticsRoutes');

// Create Express app
const app = express();

// Middleware
app.use(helmet()); // Security headers
app.use(cors()); // Enable CORS
app.use(express.json()); // Parse JSON bodies
app.use(express.urlencoded({ extended: true })); // Parse URL-encoded bodies
app.use(morgan('dev')); // Logging

// Root route
app.get('/', (req, res) => {
  res.json({ message: 'ContentForge backend is running!' });
});

// API Routes
app.use('/api/users', userRoutes);
app.use('/api/content', contentRoutes);
app.use('/api/workflows', workflowRoutes);
app.use('/api/publish', publishRoutes);
app.use('/api/analytics', analyticsRoutes);

// API Documentation route
app.get('/api/docs', (req, res) => {
  res.json({
    message: 'ContentForge API Documentation',
    version: '1.0.0',
    endpoints: {
      users: '/api/users',
      content: '/api/content',
      workflows: '/api/workflows',
      publish: '/api/publish',
      analytics: '/api/analytics'
    }
  });
});

// Error handling middleware
app.use(notFound);
app.use(errorHandler);

module.exports = app;
