# ContentForge Backend

This is the backend API for ContentForge, a digital content automation platform that streamlines the creation, management, and distribution of content across multiple digital platforms. It provides RESTful APIs for the frontend and handles integration with external services.

## Table of Contents

- [Features](#features)
- [Tech Stack](#tech-stack)
- [API Endpoints](#api-endpoints)
- [Getting Started](#getting-started)
- [Environment Variables](#environment-variables)
- [Docker](#docker)
- [Testing](#testing)
- [Project Structure](#project-structure)
- [Deployment](#deployment)
- [Contributing](#contributing)

## Features

- **User Management**: Authentication, profiles, and preferences
- **Content Management**: CRUD operations, versioning, search, and filtering
- **Workflow Orchestration**: Create, execute, and monitor workflows
- **Publishing**: Distribute content to multiple platforms (YouTube, Instagram, LinkedIn, blogs)
- **Analytics**: Track content performance across platforms

## Tech Stack

- **Node.js**: JavaScript runtime
- **Express.js**: Web framework
- **Supabase**: Database and authentication
- **Docker**: Containerization
- **n8n**: Workflow automation

## API Endpoints

### User Management

- `POST /api/users/register`: Register a new user
- `POST /api/users/login`: Login a user
- `GET /api/users/profile`: Get user profile
- `PUT /api/users/profile`: Update user profile
- `GET /api/users/preferences`: Get user preferences
- `PUT /api/users/preferences`: Update user preferences

### Content Management

- `POST /api/content`: Create new content
- `GET /api/content`: Get all content
- `GET /api/content/:id`: Get content by ID
- `PUT /api/content/:id`: Update content
- `DELETE /api/content/:id`: Delete content
- `GET /api/content/search`: Search content
- `GET /api/content/filter`: Filter content
- `GET /api/content/:id/versions`: Get content versions
- `GET /api/content/:id/versions/:versionId`: Get specific content version

### Workflow Management

- `POST /api/workflows`: Create a new workflow
- `GET /api/workflows`: Get all workflows
- `GET /api/workflows/:id`: Get workflow by ID
- `PUT /api/workflows/:id`: Update workflow
- `DELETE /api/workflows/:id`: Delete workflow
- `POST /api/workflows/:id/execute`: Execute workflow
- `GET /api/workflows/:id/status`: Get workflow status
- `POST /api/workflows/:id/cancel`: Cancel workflow execution
- `GET /api/workflows/templates`: Get workflow templates
- `POST /api/workflows/templates/:templateId/clone`: Clone workflow template

### Publishing

- `POST /api/publish`: Publish content to multiple platforms
- `GET /api/publish`: Get all published content
- `GET /api/publish/:id`: Get published content by ID
- `DELETE /api/publish/:id`: Unpublish content
- `POST /api/publish/schedule`: Schedule publication
- `GET /api/publish/schedule`: Get scheduled publications
- `PUT /api/publish/schedule/:id`: Update scheduled publication
- `DELETE /api/publish/schedule/:id`: Cancel scheduled publication

### Analytics

- `GET /api/analytics/content/:contentId`: Get analytics for specific content
- `GET /api/analytics/platform/:platform`: Get analytics for specific platform
- `GET /api/analytics/overview`: Get analytics overview
- `GET /api/analytics/performance`: Get performance metrics
- `GET /api/analytics/range`: Get analytics by date range
- `GET /api/analytics/export`: Export analytics data

## Getting Started

### Prerequisites

- Node.js 18.x or higher
- npm or yarn
- Docker and Docker Compose (for containerized development)
- Supabase account

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/your-username/contentforge.git
   cd contentforge/backend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Set up environment variables:
   ```bash
   cp .env.example .env
   # Edit .env with your Supabase credentials
   ```

   The main environment variables are:
   ```
   PORT=4000
   NODE_ENV=development
   SUPABASE_URL=your_supabase_url
   SUPABASE_ANON_KEY=your_supabase_anon_key
   N8N_URL=http://localhost:5678
   ```

4. Start the development server:
   ```bash
   npm run dev
   ```
   The server will be available at http://localhost:4000.

### Using Docker

1. **Build and start the container**:
   ```bash
   docker-compose up backend
   ```
   This will start the backend service in development mode with hot reloading.

## Environment Variables

- `PORT`: Port for the backend server (default: 4000)
- `NODE_ENV`: Environment (development, production)
- `SUPABASE_URL`: Your Supabase project URL
- `SUPABASE_ANON_KEY`: Your Supabase anonymous key
- `N8N_URL`: URL for the n8n workflow automation service

## Docker

### Development

```bash
docker-compose up
```

### Production

```bash
docker-compose -f docker-compose.prod.yml up -d
```

## Project Structure

```
backend/
├── controllers/   # Request handlers
├── middleware/    # Express middleware
├── models/        # Data models
├── routes/        # API routes
├── services/      # Business logic
├── utils/         # Utility functions
├── tests/         # Test files
├── Dockerfile     # Production Docker configuration
├── Dockerfile.dev # Development Docker configuration
├── index.js       # Application entry point
└── package.json   # Dependencies and scripts
```

## API Documentation

The backend exposes the following API endpoints:

- `GET /api/health`: Health check endpoint
- All endpoints listed in the API Endpoints section above

## Testing

Run tests with:
```bash
npm test
```

This will run the Jest test suite for the backend API.

## Deployment

The backend can be deployed using Docker. The production Dockerfile is configured to create an optimized container for deployment.

```bash
docker build -t contentforge-backend .
docker run -p 4000:4000 contentforge-backend
```

## Contributing

Please refer to the main project's CONTRIBUTING.md file for contribution guidelines.
