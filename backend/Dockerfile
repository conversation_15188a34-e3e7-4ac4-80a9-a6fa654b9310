# Use the official Node.js 18 image as the base
FROM node:18-alpine AS base

# Set the working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install production dependencies
FROM base AS dependencies
RUN npm ci --only=production

# Copy production dependencies and build the app
FROM base AS build
COPY --from=dependencies /app/node_modules ./node_modules
COPY . .

# Final image
FROM node:18-alpine AS production

# Set environment variables
ENV NODE_ENV=production
ENV PORT=4000

# Set the working directory
WORKDIR /app

# Copy from build stage
COPY --from=build /app/package*.json ./
COPY --from=build /app/node_modules ./node_modules
COPY --from=build /app/app.js ./
COPY --from=build /app/index.js ./
COPY --from=build /app/routes ./routes
COPY --from=build /app/controllers ./controllers
COPY --from=build /app/middleware ./middleware
COPY --from=build /app/services ./services
COPY --from=build /app/config ./config
COPY --from=build /app/utils ./utils
COPY --from=build /app/models ./models

# Add healthcheck
HEALTHCHECK --interval=30s --timeout=5s --start-period=5s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:4000/ || exit 1

# Expose the backend port
EXPOSE 4000

# Start the backend service
CMD ["node", "index.js"]
